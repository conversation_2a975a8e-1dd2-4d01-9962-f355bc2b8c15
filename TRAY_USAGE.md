# 系统托盘功能使用说明

## 功能概述

BitMiner 支持系统托盘功能，允许程序在后台运行而不占用任务栏空间。

## 主要功能

### 1. 最小化到托盘
- 点击窗口的关闭按钮（X）时，程序不会退出，而是最小化到系统托盘
- 托盘图标会出现在系统通知区域（通常在屏幕右下角）

### 2. 双击恢复窗口
- **左键双击**托盘图标可以恢复程序窗口
- 窗口会自动置于最前面并获得焦点
- 这是最快速的恢复窗口方式

### 3. 右键菜单
- **右键点击**托盘图标会显示上下文菜单
- 菜单选项：
  - **显示程序**：恢复程序窗口（与双击效果相同）
  - **退出**：完全退出程序

### 4. 悬停提示
- 将鼠标悬停在托盘图标上会显示程序状态信息
- 包括运行时间、生成数量、查询数量等统计信息

## 使用步骤

1. **启动程序**
   ```bash
   python bitcoin_generator.py
   ```

2. **最小化到托盘**
   - 点击窗口右上角的关闭按钮（X）
   - 程序会显示"窗口已最小化到系统托盘"的提示

3. **恢复窗口**
   - 方法一：左键双击托盘图标
   - 方法二：右键点击托盘图标，选择"显示程序"

4. **完全退出**
   - 右键点击托盘图标，选择"退出"
   - 或者在程序窗口中正常关闭

## 故障排除

### 问题：双击托盘图标没有反应
**可能原因和解决方案：**

1. **依赖库未安装**
   ```bash
   pip install pystray Pillow
   ```

2. **系统兼容性问题**
   - 确保使用的是 Windows 10/11 或支持系统托盘的 Linux 桌面环境
   - 某些精简版系统可能不支持托盘功能

3. **权限问题**
   - 以管理员身份运行程序
   - 检查系统托盘设置是否允许显示图标

### 问题：托盘图标不显示
**解决方案：**

1. **检查系统托盘设置**
   - Windows：设置 → 个性化 → 任务栏 → 选择哪些图标显示在任务栏上
   - 确保允许程序显示托盘图标

2. **重启程序**
   - 完全退出程序后重新启动

### 问题：窗口恢复后位置不正确
**解决方案：**
- 这是正常现象，程序会自动将窗口置于最前面
- 可以手动调整窗口位置

## 测试托盘功能

运行测试脚本来验证托盘功能：
```bash
python test_tray.py
```

该脚本会创建一个简单的测试窗口，用于验证：
- 托盘图标创建
- 双击恢复功能
- 右键菜单功能

## 技术说明

- 使用 `pystray` 库实现系统托盘功能
- 使用 `PIL (Pillow)` 库创建托盘图标
- 双击事件通过 `default_action` 参数处理
- 使用线程安全的方法处理GUI更新

## 注意事项

1. **后台运行**：程序最小化到托盘后会继续在后台运行
2. **资源占用**：托盘模式下程序仍会占用系统资源
3. **数据安全**：即使在托盘模式下，生成的数据仍会正常保存
4. **网络连接**：余额查询功能在托盘模式下仍会正常工作
