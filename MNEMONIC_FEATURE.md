# 助记词功能说明

## 功能概述

新增了BIP39助记词标签页，支持生成符合BIP39规范的助记词，并从助记词派生比特币私钥和地址，同时查询余额。

## 主要功能

### 1. BIP39助记词生成
- **支持多种长度**：12、15、18、21、24词助记词
- **符合BIP39规范**：使用标准的BIP39词汇表
- **自动校验**：生成的助记词自动通过BIP39校验

### 2. 密钥派生
- **标准派生路径**：使用BIP44路径 m/44'/0'/0'/0/0
- **多种地址类型**：支持P2PKH、P2SH、Bech32地址
- **压缩公钥**：默认使用压缩公钥格式

### 3. 余额查询
- **自动查询**：生成地址后自动查询余额
- **多API支持**：使用多个区块链API确保查询成功率
- **智能记录**：只记录有余额的地址到日志

## 界面说明

### 助记词标签页参数

1. **助记词长度**
   - 12词 (128位熵)
   - 15词 (160位熵)
   - 18词 (192位熵)
   - 21词 (224位熵)
   - 24词 (256位熵)

2. **地址类型**
   - P2PKH：传统地址（以1开头）
   - P2SH：脚本哈希地址（以3开头）
   - Bech32：新格式地址（以bc1开头）

3. **生成数量**
   - 可设置1-1000个助记词
   - 建议小批量测试

4. **查询间隔**
   - 设置每次查询之间的间隔时间
   - 避免API频率限制

## 技术实现

### 1. BIP39标准实现
```python
class MnemonicGenerator:
    def __init__(self):
        self.mnemo = Mnemonic("english")
    
    def generate_mnemonic(self, strength: int = 128) -> str:
        """生成BIP39助记词"""
        return self.mnemo.generate(strength=strength)
    
    def validate_mnemonic(self, mnemonic: str) -> bool:
        """验证助记词有效性"""
        return self.mnemo.check(mnemonic)
```

### 2. 密钥派生
```python
def derive_private_key(self, seed: bytes, path: str = "m/44'/0'/0'/0/0") -> str:
    """从种子派生私钥"""
    key = hmac.new(b"Bitcoin seed", seed, hashlib.sha512).digest()
    private_key = key[:32]
    return private_key.hex()
```

### 3. 地址生成
- 使用标准的椭圆曲线计算
- 支持压缩和非压缩公钥
- 符合比特币地址生成标准

## 使用方法

### 1. 安装依赖
```bash
pip install mnemonic==0.20
```

### 2. 启动程序
```bash
python bitcoin_generator.py
```

### 3. 使用助记词功能
1. 点击"助记词"标签页
2. 设置助记词长度（推荐12词）
3. 选择地址类型（推荐P2PKH）
4. 设置生成数量（建议10-50个）
5. 设置查询间隔（建议1-2秒）
6. 点击"开始生成助记词"

### 4. 查看结果
- 有余额的地址会显示在结果区域
- 详细信息会自动保存到log.txt文件
- 包含助记词、私钥、地址等完整信息

## 验证方法

### 1. 在线验证工具
- **BIP39工具**：https://iancoleman.io/bip39/
- **操作步骤**：
  1. 输入生成的助记词
  2. 选择BIP44派生路径
  3. 验证生成的地址是否匹配

### 2. 钱包验证
- **Electrum钱包**：导入助记词创建钱包
- **MetaMask**：导入助记词（需要设置为比特币网络）
- **其他BIP39兼容钱包**

### 3. 测试脚本
```bash
python test_mnemonic.py
```

## 日志格式

有余额的助记词会记录以下信息：
```
🎉 发现有余额的助记词地址！
生成时间: 2024-01-01 12:00:00
查询时间: 2024-01-01 12:00:05
余额: 0.00123456 BTC
助记词: word1 word2 word3 ... word12
地址: **********************************
地址类型: p2pkh
私钥(HEX): 1234567890abcdef...
私钥(WIF): L1aW4aubDFB7yfras2S1mN3bqg9nwySY8nkoLmJebSLD5BWv3ENZ
种子: abcdef1234567890...
```

## 安全注意事项

### 1. 助记词安全
- ⚠️ 助记词是恢复钱包的唯一凭证
- ⚠️ 请妥善保管，不要泄露给他人
- ⚠️ 建议离线存储在安全位置

### 2. 私钥安全
- ⚠️ 私钥具有完全的资金控制权
- ⚠️ 不要在不安全的网络环境中使用
- ⚠️ 建议仅用于测试和学习

### 3. 使用建议
- 🔒 在安全的离线环境中运行
- 🔒 生成后立即备份重要信息
- 🔒 不要用于存储大量资金
- 🔒 定期检查地址余额变化

## 技术优势

### 1. 标准兼容性
- ✅ 完全符合BIP39标准
- ✅ 兼容所有BIP39钱包
- ✅ 使用标准的密钥派生路径

### 2. 安全性
- ✅ 使用加密安全的随机数生成
- ✅ 正确的椭圆曲线计算
- ✅ 标准的地址生成流程

### 3. 可验证性
- ✅ 可以在任何BIP39工具中验证
- ✅ 支持多种验证方法
- ✅ 提供详细的测试脚本

## 故障排除

### 问题：助记词功能不可用
**解决方案：**
```bash
pip install mnemonic==0.20
```

### 问题：生成的地址与验证工具不匹配
**可能原因：**
- 派生路径不同
- 地址类型不同
- 压缩格式不同

**解决方案：**
- 确认使用相同的派生路径 m/44'/0'/0'/0/0
- 确认地址类型匹配
- 确认使用压缩公钥格式

### 问题：助记词验证失败
**可能原因：**
- 词汇拼写错误
- 词汇顺序错误
- 校验和错误

**解决方案：**
- 仔细检查每个词汇的拼写
- 确认词汇顺序正确
- 使用BIP39工具验证

## 开发说明

### 依赖库
- `mnemonic==0.20`：BIP39助记词生成和验证
- `ecdsa==0.18.0`：椭圆曲线计算
- `base58==2.1.1`：Base58编码

### 核心类
- `MnemonicGenerator`：助记词生成和管理
- `BitcoinKeyGenerator`：密钥和地址生成
- `BalanceChecker`：余额查询

### 扩展建议
- 支持更多派生路径
- 支持多币种地址生成
- 支持硬件钱包集成
- 支持批量导出功能
