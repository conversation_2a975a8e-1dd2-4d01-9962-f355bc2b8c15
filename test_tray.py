#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试托盘功能的简单脚本
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time

try:
    import pystray
    from PIL import Image, ImageDraw
    TRAY_AVAILABLE = True
except ImportError:
    TRAY_AVAILABLE = False
    print("警告: pystray 或 Pillow 未安装，托盘功能将不可用")

class TrayTestApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("托盘功能测试")
        self.root.geometry("400x300")
        
        self.tray_icon = None
        self.is_hidden = False
        
        self.create_widgets()
        
        if TRAY_AVAILABLE:
            self.create_tray_icon()
        
    def create_widgets(self):
        """创建界面组件"""
        frame = tk.Frame(self.root, padx=20, pady=20)
        frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(frame, text="托盘功能测试", font=("Arial", 16)).pack(pady=10)
        
        if TRAY_AVAILABLE:
            tk.Label(frame, text="✓ 托盘功能可用", fg="green").pack(pady=5)
            tk.Label(frame, text="点击关闭按钮将最小化到托盘").pack(pady=5)
            tk.Label(frame, text="右键点击托盘图标查看菜单").pack(pady=5)
        else:
            tk.Label(frame, text="✗ 托盘功能不可用", fg="red").pack(pady=5)
            tk.Label(frame, text="请安装: pip install pystray Pillow").pack(pady=5)
        
        tk.Button(frame, text="隐藏到托盘", command=self.hide_window).pack(pady=10)
        tk.Button(frame, text="退出程序", command=self.quit_application).pack(pady=5)
    
    def create_tray_icon(self):
        """创建系统托盘图标"""
        if not TRAY_AVAILABLE:
            return
            
        try:
            # 创建简单图标
            image = Image.new('RGBA', (32, 32), '#FF6B6B')
            draw = ImageDraw.Draw(image)
            draw.ellipse([4, 4, 28, 28], fill='white')
            draw.text((12, 10), "T", fill='#FF6B6B')
            
            # 创建托盘菜单
            menu = pystray.Menu(
                pystray.MenuItem("显示程序", self.show_window),
                pystray.MenuItem("退出", self.quit_application)
            )
            
            # 创建托盘图标
            self.tray_icon = pystray.Icon(
                "TrayTest",
                image,
                "托盘功能测试",
                menu
            )
            
        except Exception as e:
            print(f"创建托盘图标失败: {e}")
            self.tray_icon = None
    
    def show_window(self, icon=None, item=None):
        """显示主窗口"""
        if self.is_hidden:
            self.root.deiconify()
            self.root.lift()
            self.root.focus_force()
            self.is_hidden = False
    
    def hide_window(self):
        """隐藏主窗口到托盘"""
        if not TRAY_AVAILABLE or not self.tray_icon:
            messagebox.showwarning("警告", "托盘功能不可用")
            return
            
        if not self.is_hidden:
            self.root.withdraw()
            self.is_hidden = True
            
            # 启动托盘图标
            if not hasattr(self.tray_icon, '_running'):
                tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
                tray_thread.start()
    
    def quit_application(self, icon=None, item=None):
        """退出应用程序"""
        if self.tray_icon:
            self.tray_icon.stop()
        
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        def on_closing():
            if TRAY_AVAILABLE and self.tray_icon:
                self.hide_window()
            else:
                self.quit_application()
        
        self.root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # 启动托盘图标
        if TRAY_AVAILABLE and self.tray_icon:
            tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
            tray_thread.start()
        
        self.root.mainloop()

if __name__ == "__main__":
    app = TrayTestApp()
    app.run()
