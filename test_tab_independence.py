#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试两个tab的独立性
验证随机密钥和助记词功能是否相互独立
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_tab_independence():
    """测试tab独立性"""
    print("Tab独立性测试")
    print("=" * 50)
    
    try:
        from bitcoin_generator import BitcoinGeneratorGUI
        
        # 创建GUI实例
        app = BitcoinGeneratorGUI()
        
        print("✅ GUI创建成功")
        
        # 检查初始状态
        print("\n检查初始状态:")
        print(f"随机密钥运行状态: {app.random_is_running}")
        print(f"助记词运行状态: {app.mnemonic_is_running}")
        print(f"随机密钥统计: {app.random_stats}")
        print(f"助记词统计: {app.mnemonic_stats}")
        
        # 验证状态变量独立性
        assert not app.random_is_running, "随机密钥初始状态应为False"
        assert not app.mnemonic_is_running, "助记词初始状态应为False"
        assert app.random_stats != app.mnemonic_stats, "统计对象应该是独立的"
        
        print("✅ 初始状态检查通过")
        
        # 模拟启动随机密钥功能
        print("\n模拟启动随机密钥功能:")
        app.random_is_running = True
        app.random_stats['start_time'] = time.time()
        app.random_stats['generated'] = 10
        app.random_stats['checked'] = 8
        app.random_stats['found_with_balance'] = 1
        
        print(f"随机密钥运行状态: {app.random_is_running}")
        print(f"助记词运行状态: {app.mnemonic_is_running}")
        print(f"随机密钥统计: {app.random_stats}")
        print(f"助记词统计: {app.mnemonic_stats}")
        
        # 验证助记词状态未受影响
        assert app.mnemonic_is_running == False, "助记词状态不应受随机密钥影响"
        assert app.mnemonic_stats['generated'] == 0, "助记词统计不应受随机密钥影响"
        
        print("✅ 随机密钥启动后，助记词状态保持独立")
        
        # 模拟启动助记词功能
        print("\n模拟启动助记词功能:")
        app.mnemonic_is_running = True
        app.mnemonic_stats['start_time'] = time.time()
        app.mnemonic_stats['generated'] = 5
        app.mnemonic_stats['checked'] = 4
        app.mnemonic_stats['found_with_balance'] = 0
        
        print(f"随机密钥运行状态: {app.random_is_running}")
        print(f"助记词运行状态: {app.mnemonic_is_running}")
        print(f"随机密钥统计: {app.random_stats}")
        print(f"助记词统计: {app.mnemonic_stats}")
        
        # 验证两个功能可以同时运行且互不影响
        assert app.random_is_running == True, "随机密钥状态不应受助记词影响"
        assert app.mnemonic_is_running == True, "助记词状态应为True"
        assert app.random_stats['generated'] == 10, "随机密钥统计不应受助记词影响"
        assert app.mnemonic_stats['generated'] == 5, "助记词统计应正确"
        
        print("✅ 两个功能可以同时运行且互不影响")
        
        # 测试统计显示
        print("\n测试统计显示:")
        app.update_stats_display()
        stats_text = app.stats_var.get()
        print(f"当前统计显示: {stats_text}")
        
        # 应该显示助记词的统计（因为它是最后启动的）
        assert "助记词" in stats_text, "应该显示助记词统计"
        
        print("✅ 统计显示正确")
        
        # 模拟停止随机密钥
        print("\n模拟停止随机密钥:")
        app.random_is_running = False
        
        print(f"随机密钥运行状态: {app.random_is_running}")
        print(f"助记词运行状态: {app.mnemonic_is_running}")
        
        # 验证助记词仍在运行
        assert app.random_is_running == False, "随机密钥应已停止"
        assert app.mnemonic_is_running == True, "助记词应继续运行"
        
        print("✅ 停止随机密钥后，助记词继续运行")
        
        # 模拟停止助记词
        print("\n模拟停止助记词:")
        app.mnemonic_is_running = False
        
        print(f"随机密钥运行状态: {app.random_is_running}")
        print(f"助记词运行状态: {app.mnemonic_is_running}")
        
        # 验证两个功能都已停止
        assert app.random_is_running == False, "随机密钥应已停止"
        assert app.mnemonic_is_running == False, "助记词应已停止"
        
        print("✅ 两个功能都已正确停止")
        
        print("\n" + "=" * 50)
        print("✅ 所有测试通过！两个tab功能完全独立")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_message_types():
    """测试消息类型的独立性"""
    print("\n消息类型独立性测试")
    print("-" * 30)
    
    # 定义消息类型
    random_messages = [
        'random_progress_update',
        'random_balance_found',
        'generation_stopped'
    ]
    
    mnemonic_messages = [
        'mnemonic_progress_update', 
        'mnemonic_balance_found',
        'mnemonic_generation_stopped'
    ]
    
    print("随机密钥消息类型:")
    for msg in random_messages:
        print(f"  - {msg}")
    
    print("\n助记词消息类型:")
    for msg in mnemonic_messages:
        print(f"  - {msg}")
    
    # 验证没有重复
    all_messages = random_messages + mnemonic_messages
    unique_messages = set(all_messages)
    
    assert len(all_messages) == len(unique_messages), "消息类型不应重复"
    
    print("\n✅ 消息类型完全独立，无重复")
    
    return True

def main():
    """主函数"""
    try:
        print("BitMiner Tab独立性测试")
        print("=" * 60)
        
        # 测试tab独立性
        test1_result = test_tab_independence()
        
        # 测试消息类型独立性
        test2_result = test_message_types()
        
        if test1_result and test2_result:
            print("\n🎉 所有测试通过！")
            print("✅ 随机密钥和助记词功能完全独立")
            print("✅ 两个tab可以独立运行，互不影响")
            print("✅ 统计信息和状态管理正确分离")
        else:
            print("\n❌ 部分测试失败")
            
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
