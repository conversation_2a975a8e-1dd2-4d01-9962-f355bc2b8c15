#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
自动安装BitMiner所需的Python库
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package} 安装失败")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    print("BitMiner 依赖安装脚本")
    print("=" * 40)
    
    # 需要安装的包列表
    packages = [
        ("base58", "base58==2.1.1"),
        ("requests", "requests==2.31.0"),
        ("pystray", "pystray==0.19.4"),
        ("PIL", "Pillow==10.0.0")
    ]
    
    # 检查当前安装状态
    print("\n检查当前安装状态:")
    all_installed = True
    
    for import_name, package_spec in packages:
        if check_package(import_name):
            print(f"✓ {import_name} 已安装")
        else:
            print(f"✗ {import_name} 未安装")
            all_installed = False
    
    if all_installed:
        print("\n所有依赖都已安装！")
        return
    
    # 安装缺失的包
    print("\n开始安装缺失的依赖...")
    
    failed_packages = []
    
    for import_name, package_spec in packages:
        if not check_package(import_name):
            if not install_package(package_spec):
                failed_packages.append(package_spec)
    
    # 安装结果
    print("\n" + "=" * 40)
    if not failed_packages:
        print("✓ 所有依赖安装完成！")
        print("\n现在可以运行程序了:")
        print("  python bitcoin_generator.py")
    else:
        print("✗ 以下包安装失败:")
        for package in failed_packages:
            print(f"  - {package}")
        print("\n请手动安装失败的包:")
        print("  pip install " + " ".join(failed_packages))
    
    print("\n如果遇到权限问题，请尝试:")
    print("  pip install --user " + " ".join([p[1] for p in packages]))

if __name__ == "__main__":
    main()
