#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比特币密钥和地址生成器
支持多种密钥类型和地址格式的GUI程序
增强版：支持余额查询、并发处理、循环生成
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import queue
import os
import datetime
import secrets
import hashlib
import base58
import requests
import time
import json
from typing import Tuple, Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import pystray
from PIL import Image, ImageDraw
import io
import base64


class BalanceChecker:
    """比特币地址余额查询类"""

    def __init__(self):
        # 多个API服务，提高查询成功率
        self.api_services = [
            {
                'name': 'Blockstream',
                'url': 'https://blockstream.info/api/address/{address}',
                'balance_key': 'chain_stats',
                'sub_key': 'funded_txo_sum',
                'rate_limit': 0.5,  # 较保守的请求间隔
                'headers': {'User-Agent': 'BitMiner/1.0'}
            },
            {
                'name': 'BlockCypher',
                'url': 'https://api.blockcypher.com/v1/btc/main/addrs/{address}/balance',
                'balance_key': 'balance',
                'rate_limit': 0.3,
                'headers': {'User-Agent': 'BitMiner/1.0'}
            },
            {
                'name': 'Blockchain.info',
                'url': 'https://blockchain.info/q/addressbalance/{address}',
                'balance_key': None,  # 直接返回余额数值
                'rate_limit': 1.0,  # blockchain.info 限制较严格
                'headers': {'User-Agent': 'BitMiner/1.0'}
            }
        ]
        self.current_api_index = 0
        self.last_request_time = {}  # 为每个API单独记录最后请求时间

        # 初始化每个API的最后请求时间
        for i in range(len(self.api_services)):
            self.last_request_time[i] = 0

    def check_balance(self, address: str, timeout: int = 10) -> Tuple[float, str]:
        """
        查询地址余额
        返回: (余额(BTC), 错误信息)
        """
        last_error = ""

        for attempt in range(len(self.api_services)):
            api = self.api_services[self.current_api_index]

            try:
                # 控制请求频率 - 为每个API单独控制
                current_time = time.time()
                elapsed = current_time - self.last_request_time[self.current_api_index]
                if elapsed < api['rate_limit']:
                    sleep_time = api['rate_limit'] - elapsed
                    print(f"等待 {sleep_time:.1f} 秒以避免 {api['name']} API限制...")
                    time.sleep(sleep_time)

                url = api['url'].format(address=address)
                headers = api.get('headers', {})

                print(f"正在使用 {api['name']} API查询地址: {address[:20]}...")
                response = requests.get(url, headers=headers, timeout=timeout)
                self.last_request_time[self.current_api_index] = time.time()

                # 检查HTTP状态码
                if response.status_code == 200:
                    # 尝试解析余额
                    result = self._parse_balance(response, api)

                    if result['success']:
                        balance_btc = result['balance'] / 100000000  # 转换为BTC
                        print(f"✅ {api['name']} 查询成功: {balance_btc:.8f} BTC")
                        return balance_btc, ""
                    else:
                        # 解析失败，记录错误并尝试下一个API
                        last_error = f"{api['name']}: {result['error']}"
                        print(f"❌ {api['name']} 解析失败: {result['error']}")

                elif response.status_code == 429:
                    # 速率限制
                    last_error = f"{api['name']}: 请求频率限制"
                    print(f"⚠️ {api['name']} 请求频率限制")

                else:
                    # 其他HTTP错误
                    last_error = f"{api['name']}: HTTP {response.status_code}"
                    print(f"❌ {api['name']} HTTP错误: {response.status_code}")

                    # 对于某些错误，打印响应内容以便调试
                    if response.status_code in [400, 404, 500]:
                        print(f"响应内容: {response.text[:200]}")

            except requests.exceptions.Timeout:
                last_error = f"{api['name']}: 请求超时"

            except requests.exceptions.RequestException as e:
                last_error = f"{api['name']}: 网络错误 - {str(e)}"

            except Exception as e:
                last_error = f"{api['name']}: 未知错误 - {str(e)}"

            # 切换到下一个API
            self.current_api_index = (self.current_api_index + 1) % len(self.api_services)

        # 所有API都失败了
        return 0.0, f"所有API查询失败，最后错误: {last_error}"

    def _parse_balance(self, response: requests.Response, api: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析API响应中的余额
        返回: {'success': bool, 'balance': int, 'error': str}
        """
        try:
            if api['balance_key'] is None:
                # 直接返回数值的API (如 blockchain.info)
                response_text = response.text.strip()

                # 检查是否是错误响应
                if self._is_error_response(response_text):
                    return {
                        'success': False,
                        'balance': 0,
                        'error': f"API返回错误: {response_text}"
                    }

                # 尝试解析为数字
                try:
                    balance = int(response_text)
                    return {
                        'success': True,
                        'balance': balance,
                        'error': ""
                    }
                except ValueError:
                    return {
                        'success': False,
                        'balance': 0,
                        'error': f"无法解析响应为数字: {response_text}"
                    }

            else:
                # JSON格式的API
                try:
                    data = response.json()
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'balance': 0,
                        'error': "响应不是有效的JSON格式"
                    }

                # 提取余额
                if api['name'] == 'Blockstream' and 'sub_key' in api:
                    balance = data.get(api['balance_key'], {}).get(api['sub_key'], 0)
                else:
                    balance = data.get(api['balance_key'], 0)

                return {
                    'success': True,
                    'balance': balance,
                    'error': ""
                }

        except Exception as e:
            return {
                'success': False,
                'balance': 0,
                'error': f"解析异常: {str(e)}"
            }

    def _is_error_response(self, response_text: str) -> bool:
        """检查响应文本是否是错误信息"""
        error_indicators = [
            'rate limited',
            'rate limit',
            'too many requests',
            'error',
            'invalid',
            'not found',
            'service unavailable',
            'timeout',
            'forbidden',
            'unauthorized'
        ]

        response_lower = response_text.lower()
        return any(indicator in response_lower for indicator in error_indicators)


class BitcoinKeyGenerator:
    """比特币密钥和地址生成器核心类"""
    
    def __init__(self):
        self.secp256k1_order = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
    
    def generate_private_key(self, key_type: str = "hex") -> str:
        """生成私钥"""
        # 生成32字节的随机数
        private_key_bytes = secrets.randbits(256).to_bytes(32, 'big')
        
        # 确保私钥在有效范围内
        private_key_int = int.from_bytes(private_key_bytes, 'big')
        if private_key_int >= self.secp256k1_order:
            private_key_int = private_key_int % self.secp256k1_order
            private_key_bytes = private_key_int.to_bytes(32, 'big')
        
        if key_type == "hex":
            return private_key_bytes.hex()
        elif key_type == "wif":
            return self._to_wif(private_key_bytes)
        elif key_type == "wif_compressed":
            return self._to_wif(private_key_bytes, compressed=True)
        else:
            return private_key_bytes.hex()
    
    def _to_wif(self, private_key_bytes: bytes, compressed: bool = False) -> str:
        """转换为WIF格式"""
        # 添加版本字节 (0x80 for mainnet)
        extended_key = b'\x80' + private_key_bytes
        
        # 如果是压缩格式，添加压缩标志
        if compressed:
            extended_key += b'\x01'
        
        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
        
        # 组合并编码
        wif_key = extended_key + checksum
        return base58.b58encode(wif_key).decode('utf-8')
    
    def generate_address(self, private_key_hex: str, address_type: str = "p2pkh") -> str:
        """根据私钥生成地址"""
        try:
            # 这里使用简化的地址生成逻辑
            # 实际应用中需要使用完整的椭圆曲线计算
            private_key_bytes = bytes.fromhex(private_key_hex)
            
            # 简化的公钥生成（实际需要椭圆曲线计算）
            public_key_hash = hashlib.sha256(private_key_bytes).digest()
            
            if address_type == "p2pkh":
                return self._generate_p2pkh_address(public_key_hash)
            elif address_type == "p2sh":
                return self._generate_p2sh_address(public_key_hash)
            elif address_type == "bech32":
                return self._generate_bech32_address(public_key_hash)
            else:
                return self._generate_p2pkh_address(public_key_hash)
        except Exception as e:
            return f"地址生成错误: {str(e)}"
    
    def _generate_p2pkh_address(self, public_key_hash: bytes) -> str:
        """生成P2PKH地址"""
        # 添加版本字节 (0x00 for mainnet P2PKH)
        versioned_hash = b'\x00' + hashlib.new('ripemd160', public_key_hash).digest()
        
        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # 组合并编码
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode('utf-8')
    
    def _generate_p2sh_address(self, public_key_hash: bytes) -> str:
        """生成P2SH地址"""
        # 添加版本字节 (0x05 for mainnet P2SH)
        versioned_hash = b'\x05' + hashlib.new('ripemd160', public_key_hash).digest()
        
        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # 组合并编码
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode('utf-8')
    
    def _generate_bech32_address(self, public_key_hash: bytes) -> str:
        """生成Bech32地址（简化版本）"""
        # 这是一个简化的bech32地址生成
        # 实际应用中需要完整的bech32编码
        hash160 = hashlib.new('ripemd160', public_key_hash).digest()
        return f"bc1q{hash160.hex()}"


class BitcoinGeneratorGUI:
    """比特币生成器GUI界面 - 增强版"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BitMiner")
        self.root.geometry("600x600")
        self.root.resizable(True, True)

        # 初始化生成器和余额查询器
        self.generator = BitcoinKeyGenerator()
        self.balance_checker = BalanceChecker()

        # 创建队列用于线程通信
        self.result_queue = queue.Queue()
        self.balance_queue = queue.Queue()

        # 控制变量
        self.is_running = False
        self.stop_event = threading.Event()
        self.executor = None

        # 统计信息
        self.stats = {
            'generated': 0,
            'checked': 0,
            'found_with_balance': 0,
            'start_time': None
        }

        # 托盘相关变量
        self.tray_icon = None
        self.is_hidden = False

        # 创建界面
        self.create_widgets()

        # 创建托盘图标
        self.create_tray_icon()

        # 启动结果处理
        self.process_results()

    def create_tray_icon(self):
        """创建系统托盘图标"""
        try:
            # 创建一个简单的比特币图标
            icon_image = self.create_bitcoin_icon()

            # 创建托盘菜单
            menu = pystray.Menu(
                pystray.MenuItem("显示程序", self.show_window),
                pystray.MenuItem("退出", self.quit_application)
            )

            # 创建托盘图标，使用default_action处理双击
            self.tray_icon = pystray.Icon(
                "BitMiner",
                icon_image,
                "BitMiner - 比特币密钥生成器",
                menu,
                default_action=self.on_tray_activate  # 双击时的默认动作
            )

        except Exception as e:
            print(f"创建托盘图标失败: {e}")
            self.tray_icon = None

    def on_tray_activate(self, icon, item):
        """处理托盘图标激活事件（双击）"""
        print("托盘图标被双击，尝试显示窗口")  # 调试信息
        # 使用线程安全的方式调用GUI方法
        self.root.after(0, self.show_window_safe)

    def show_window_safe(self):
        """线程安全的显示窗口方法"""
        try:
            if self.is_hidden:
                print("正在恢复窗口显示...")

                # 恢复窗口显示
                self.root.deiconify()

                # 确保窗口状态正常
                self.root.state('normal')

                # 确保窗口在最前面
                self.root.lift()
                self.root.attributes('-topmost', True)
                self.root.after(100, lambda: self.root.attributes('-topmost', False))

                # 获取焦点
                self.root.focus_force()

                # 更新状态
                self.is_hidden = False

                print("窗口已成功恢复显示")
            else:
                print("窗口已经显示，无需恢复")

        except Exception as e:
            print(f"恢复窗口显示时出错: {e}")

    def create_bitcoin_icon(self):
        """创建比特币图标"""
        try:
            # 创建一个32x32的图标
            size = 32
            image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(image)

            # 绘制比特币符号 ₿
            # 外圆
            draw.ellipse([2, 2, size-2, size-2], fill='#F7931A', outline='#E8831A', width=2)

            # 内部的B字符（简化版本）
            # 垂直线
            draw.rectangle([size//3, size//4, size//3+2, 3*size//4], fill='white')

            # 上半部分横线
            draw.rectangle([size//3, size//4, 2*size//3-2, size//4+2], fill='white')
            draw.rectangle([size//3, size//2-1, 2*size//3-4, size//2+1], fill='white')

            # 下半部分横线
            draw.rectangle([size//3, 3*size//4-2, 2*size//3, 3*size//4], fill='white')

            return image

        except Exception:
            # 如果创建图标失败，使用简单的彩色方块
            size = 32
            image = Image.new('RGBA', (size, size), '#F7931A')
            return image

    def show_window(self, icon=None, item=None):
        """显示主窗口（支持右键菜单调用）"""
        print("通过菜单显示窗口")
        # 使用线程安全的方法
        self.root.after(0, self.show_window_safe)

    def hide_window(self):
        """隐藏主窗口到托盘"""
        if not self.is_hidden:
            self.root.withdraw()
            self.is_hidden = True

            print("窗口已最小化到系统托盘")  # 调试信息
            print("双击托盘图标可恢复窗口显示")  # 用户提示

            # 启动托盘图标（如果还没有启动）
            if self.tray_icon and not hasattr(self.tray_icon, '_running'):
                tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
                tray_thread.start()

    def quit_application(self, icon=None, item=None):
        """退出应用程序"""
        if self.is_running:
            self.stop_generation()

        # 停止托盘图标
        if self.tray_icon:
            self.tray_icon.stop()

        # 销毁主窗口
        self.root.quit()
        self.root.destroy()

    def update_tray_tooltip(self):
        """更新托盘图标的提示信息"""
        if self.tray_icon and self.stats['start_time']:
            elapsed = time.time() - self.stats['start_time']
            hours = int(elapsed // 3600)
            minutes = int((elapsed % 3600) // 60)

            tooltip = (
                f"BitMiner - 运行中\n"
                f"运行时间: {hours:02d}:{minutes:02d}\n"
                f"已生成: {self.stats['generated']}\n"
                f"已查询: {self.stats['checked']}\n"
                f"发现有余额: {self.stats['found_with_balance']}"
            )

            try:
                self.tray_icon.title = tooltip
            except:
                pass
        elif self.tray_icon:
            self.tray_icon.title = "BitMiner - 就绪"

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(3, weight=1)

        # 第一行：密钥类型和地址类型
        ttk.Label(main_frame, text="密钥类型:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.key_type_var = tk.StringVar(value="wif")
        key_type_combo = ttk.Combobox(main_frame, textvariable=self.key_type_var,
                                     values=["wif", "hex", "wif_compressed"],
                                     state="readonly", width=15)
        key_type_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        ttk.Label(main_frame, text="地址类型:").grid(row=0, column=2, sticky=tk.W, pady=5)
        self.address_type_var = tk.StringVar(value="p2pkh")
        address_type_combo = ttk.Combobox(main_frame, textvariable=self.address_type_var,
                                         values=["p2pkh", "p2sh", "bech32"],
                                         state="readonly", width=15)
        address_type_combo.grid(row=0, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 第二行：生成参数
        ttk.Label(main_frame, text="每批生成数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.batch_size_var = tk.StringVar(value="10")
        batch_size_entry = ttk.Entry(main_frame, textvariable=self.batch_size_var, width=15)
        batch_size_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        ttk.Label(main_frame, text="查询线程数:").grid(row=1, column=2, sticky=tk.W, pady=5)
        self.thread_count_var = tk.StringVar(value="5")
        thread_count_entry = ttk.Entry(main_frame, textvariable=self.thread_count_var, width=15)
        thread_count_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 第三行：查询参数
        ttk.Label(main_frame, text="查询间隔(秒):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.query_interval_var = tk.StringVar(value="0.5")
        query_interval_entry = ttk.Entry(main_frame, textvariable=self.query_interval_var, width=15)
        query_interval_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        ttk.Label(main_frame, text="查询超时(秒):").grid(row=2, column=2, sticky=tk.W, pady=5)
        self.query_timeout_var = tk.StringVar(value="10")
        query_timeout_entry = ttk.Entry(main_frame, textvariable=self.query_timeout_var, width=15)
        query_timeout_entry.grid(row=2, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=20)

        # 开始按钮
        self.start_btn = ttk.Button(button_frame, text="开始生成和查询",
                                   command=self.start_continuous_generation)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        # 停止按钮
        self.stop_btn = ttk.Button(button_frame, text="停止",
                                  command=self.stop_generation, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 清空按钮
        clear_btn = ttk.Button(button_frame, text="清空结果", command=self.clear_results)
        clear_btn.pack(side=tk.LEFT, padx=5)

        # 统计信息框架
        stats_frame = ttk.LabelFrame(main_frame, text="运行统计", padding="10")
        stats_frame.grid(row=4, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=10)

        # 统计标签
        self.stats_var = tk.StringVar(value="就绪")
        ttk.Label(stats_frame, textvariable=self.stats_var).pack()

        # 进度信息
        self.progress_var = tk.StringVar(value="")
        ttk.Label(main_frame, textvariable=self.progress_var).grid(row=5, column=0, columnspan=4, pady=5)

        # 结果显示区域
        ttk.Label(main_frame, text="有余额的地址记录:").grid(row=6, column=0, columnspan=4, sticky=tk.W, pady=(20, 5))

        self.result_text = scrolledtext.ScrolledText(main_frame, height=15, width=120)
        self.result_text.grid(row=7, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)

        # 配置结果区域的网格权重
        main_frame.rowconfigure(7, weight=1)
    
    def start_continuous_generation(self):
        """开始连续生成和查询"""
        if self.is_running:
            return

        # 验证参数
        try:
            batch_size = int(self.batch_size_var.get())
            thread_count = int(self.thread_count_var.get())
            query_interval = float(self.query_interval_var.get())
            query_timeout = int(self.query_timeout_var.get())

            if batch_size <= 0 or batch_size > 1000:
                messagebox.showerror("错误", "每批生成数必须在1-1000之间")
                return
            if thread_count <= 0 or thread_count > 20:
                messagebox.showerror("错误", "查询线程数必须在1-20之间")
                return
            if query_interval < 0.1 or query_interval > 10:
                messagebox.showerror("错误", "查询间隔必须在0.1-10秒之间")
                return
            if query_timeout < 5 or query_timeout > 60:
                messagebox.showerror("错误", "查询超时必须在5-60秒之间")
                return

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数值")
            return

        # 初始化状态
        self.is_running = True
        self.stop_event.clear()
        self.stats['generated'] = 0
        self.stats['checked'] = 0
        self.stats['found_with_balance'] = 0
        self.stats['start_time'] = time.time()

        # 更新界面状态
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.progress_var.set("正在启动...")

        # 创建线程池
        self.executor = ThreadPoolExecutor(max_workers=thread_count)

        # 启动生成线程
        generation_thread = threading.Thread(
            target=self.continuous_generation_worker,
            args=(batch_size, query_interval, query_timeout),
            daemon=True
        )
        generation_thread.start()

    def stop_generation(self):
        """停止生成和查询"""
        if not self.is_running:
            return

        self.is_running = False
        self.stop_event.set()

        # 关闭线程池
        if self.executor:
            self.executor.shutdown(wait=False)
            self.executor = None

        # 更新界面状态
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress_var.set("已停止")

    def continuous_generation_worker(self, batch_size: int, query_interval: float, query_timeout: int):
        """连续生成工作线程"""
        try:
            while self.is_running and not self.stop_event.is_set():
                try:
                    # 生成一批密钥和地址
                    batch_data = self.generate_batch(batch_size)

                    if not batch_data:
                        print("⚠️ 生成批次为空，等待后重试...")
                        time.sleep(1)
                        continue

                    # 提交余额查询任务
                    future_to_data = {}
                    for data in batch_data:
                        if self.stop_event.is_set():
                            break

                        future = self.executor.submit(
                            self.check_address_balance,
                            data['address'],
                            data,
                            query_timeout
                        )
                        future_to_data[future] = data

                    # 处理查询结果 - 改进的异常处理
                    try:
                        # 使用较长的超时时间，并处理未完成的futures
                        completed_count = 0

                        for future in as_completed(future_to_data, timeout=query_timeout * 2):
                            if self.stop_event.is_set():
                                break

                            try:
                                result = future.result(timeout=1)  # 给每个future一个短超时
                                completed_count += 1
                                if result:
                                    # 发送有余额的结果
                                    self.result_queue.put(('balance_found', result))
                            except Exception as e:
                                # 单个查询失败，记录但继续
                                completed_count += 1
                                print(f"单个查询失败: {e}")

                    except Exception as e:
                        # as_completed超时或其他异常
                        print(f"批次查询异常: {e}")

                        # 取消未完成的futures
                        unfinished_count = 0
                        for future in future_to_data:
                            if not future.done():
                                future.cancel()
                                unfinished_count += 1

                        if unfinished_count > 0:
                            print(f"取消了 {unfinished_count} 个未完成的查询任务")

                        # 继续运行，不抛出异常

                except Exception as batch_error:
                    # 单个批次处理失败，记录错误但继续下一批次
                    print(f"❌ 批次处理失败: {batch_error}")
                    self.result_queue.put(('progress_update', {
                        'address': 'batch_error',
                        'balance': 0,
                        'error': f"批次错误: {str(batch_error)}"
                    }))
                    # 等待一段时间后继续
                    time.sleep(2)

                # 等待间隔
                if not self.stop_event.wait(query_interval):
                    continue

        except Exception as e:
            error_msg = f"生成过程错误: {str(e)}"
            print(f"❌ {error_msg}")
            self.result_queue.put(('error_with_restart', error_msg))
        finally:
            self.result_queue.put(('generation_stopped', None))

    def generate_batch(self, batch_size: int) -> list:
        """生成一批密钥和地址"""
        try:
            key_type = self.key_type_var.get()
            address_type = self.address_type_var.get()

            batch_data = []

            for i in range(batch_size):
                if self.stop_event.is_set():
                    break

                # 生成私钥
                private_key = self.generator.generate_private_key(key_type)

                # 生成地址（使用hex格式的私钥）
                if key_type.startswith("wif"):
                    hex_key = self.generator.generate_private_key("hex")
                else:
                    hex_key = private_key

                address = self.generator.generate_address(hex_key, address_type)

                # 创建数据记录
                data = {
                    'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'private_key': private_key,
                    'address': address,
                    'key_type': key_type,
                    'address_type': address_type,
                    'hex_key': hex_key
                }
                batch_data.append(data)

                # 更新生成统计
                self.stats['generated'] += 1

            return batch_data

        except Exception as e:
            self.result_queue.put(('error', f"生成批次错误: {str(e)}"))
            return []

    def check_address_balance(self, address: str, data: dict, timeout: int) -> Optional[dict]:
        """检查地址余额"""
        try:
            balance, error = self.balance_checker.check_balance(address, timeout)

            # 更新查询统计
            self.stats['checked'] += 1

            # 发送进度更新
            self.result_queue.put(('progress_update', {
                'address': address,
                'balance': balance,
                'error': error
            }))

            # 只有余额大于0才返回结果
            if balance > 0:
                self.stats['found_with_balance'] += 1
                data['balance'] = balance
                data['balance_check_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                return data

            return None

        except Exception as e:
            return None


    
    def process_results(self):
        """处理生成结果（主线程）"""
        try:
            while True:
                msg_type, data = self.result_queue.get_nowait()

                if msg_type == 'progress_update':
                    self.update_progress_display(data)
                elif msg_type == 'balance_found':
                    self.display_balance_result(data)
                    self.save_balance_to_log(data)
                elif msg_type == 'generation_stopped':
                    self.progress_var.set("生成已停止")
                elif msg_type == 'error':
                    messagebox.showerror("错误", f"运行错误: {data}")
                    self.stop_generation()
                elif msg_type == 'error_with_restart':
                    # 错误发生但自动重启
                    self.handle_error_with_restart(data)

        except queue.Empty:
            pass

        # 更新统计信息
        self.update_stats_display()

        # 继续检查队列
        self.root.after(100, self.process_results)

    def handle_error_with_restart(self, error_msg: str):
        """处理错误并自动重启"""
        print(f"🔄 检测到错误，准备自动重启: {error_msg}")

        # 显示错误信息但不停止程序
        self.progress_var.set(f"发生错误，正在重启... {error_msg[:50]}")

        # 等待一段时间后自动重启
        def auto_restart():
            if self.is_running:  # 只有在运行状态下才重启
                print("🔄 自动重启生成和查询流程...")

                # 重置统计信息（可选）
                # self.stats['generated'] = 0
                # self.stats['checked'] = 0
                # self.stats['found_with_balance'] = 0

                # 获取当前参数
                try:
                    batch_size = int(self.batch_size_var.get())
                    query_interval = float(self.query_interval_var.get())
                    query_timeout = int(self.query_timeout_var.get())

                    # 重新创建线程池（如果需要）
                    if self.executor:
                        self.executor.shutdown(wait=False)

                    thread_count = int(self.thread_count_var.get())
                    self.executor = ThreadPoolExecutor(max_workers=thread_count)

                    # 启动新的生成线程
                    generation_thread = threading.Thread(
                        target=self.continuous_generation_worker,
                        args=(batch_size, query_interval, query_timeout),
                        daemon=True
                    )
                    generation_thread.start()

                    self.progress_var.set("已自动重启，继续运行...")
                    print("✅ 自动重启完成")

                except Exception as e:
                    print(f"❌ 自动重启失败: {e}")
                    self.progress_var.set(f"自动重启失败: {str(e)}")
                    # 如果自动重启失败，停止程序
                    self.stop_generation()

        # 3秒后自动重启
        self.root.after(3000, auto_restart)

    def update_progress_display(self, data: dict):
        """更新进度显示"""
        address = data['address']
        balance = data['balance']
        error = data['error']

        if error:
            status = f"查询失败: {error}"
        else:
            status = f"余额: {balance:.8f} BTC"

        self.progress_var.set(f"正在查询 {address[:20]}... - {status}")

    def update_stats_display(self):
        """更新统计信息显示"""
        if self.stats['start_time']:
            elapsed = time.time() - self.stats['start_time']
            hours = int(elapsed // 3600)
            minutes = int((elapsed % 3600) // 60)
            seconds = int(elapsed % 60)

            generated_rate = self.stats['generated'] / elapsed if elapsed > 0 else 0
            checked_rate = self.stats['checked'] / elapsed if elapsed > 0 else 0

            stats_text = (
                f"运行时间: {hours:02d}:{minutes:02d}:{seconds:02d} | "
                f"已生成: {self.stats['generated']} ({generated_rate:.1f}/秒) | "
                f"已查询: {self.stats['checked']} ({checked_rate:.1f}/秒) | "
                f"发现有余额: {self.stats['found_with_balance']}"
            )
        else:
            stats_text = "就绪"

        self.stats_var.set(stats_text)

    def display_balance_result(self, result: dict):
        """显示有余额的结果"""
        output = f"🎉 发现有余额的地址！\n"
        output += f"生成时间: {result['timestamp']}\n"
        output += f"查询时间: {result['balance_check_time']}\n"
        output += f"余额: {result['balance']:.8f} BTC\n"
        output += f"地址: {result['address']}\n"
        output += f"地址类型: {result['address_type']}\n"
        output += f"密钥类型: {result['key_type']}\n"
        output += f"私钥: {result['private_key']}\n"
        output += "=" * 100 + "\n"

        self.result_text.insert(tk.END, output)

        # 滚动到底部
        self.result_text.see(tk.END)

        # 高亮显示
        self.result_text.tag_add("highlight", "end-9l", "end-1l")
        self.result_text.tag_config("highlight", background="yellow")

    def save_balance_to_log(self, result: dict):
        """保存有余额的结果到日志文件"""
        try:
            log_file = os.path.join(os.path.dirname(__file__), "log.txt")

            with open(log_file, "a", encoding="utf-8") as f:
                f.write(f"🎉 发现有余额的地址！\n")
                f.write(f"生成时间: {result['timestamp']}\n")
                f.write(f"查询时间: {result['balance_check_time']}\n")
                f.write(f"余额: {result['balance']:.8f} BTC\n")
                f.write(f"地址: {result['address']}\n")
                f.write(f"地址类型: {result['address_type']}\n")
                f.write(f"密钥类型: {result['key_type']}\n")
                f.write(f"私钥: {result['private_key']}\n")
                f.write("=" * 100 + "\n")

        except Exception as e:
            messagebox.showerror("错误", f"保存日志文件失败: {str(e)}")

    def display_results(self, results):
        """显示生成结果（保留兼容性）"""
        for result in results:
            output = f"时间: {result['timestamp']}\n"
            output += f"密钥类型: {result['key_type']}\n"
            output += f"私钥: {result['private_key']}\n"
            output += f"地址类型: {result['address_type']}\n"
            output += f"地址: {result['address']}\n"
            output += "-" * 80 + "\n"

            self.result_text.insert(tk.END, output)

        # 滚动到底部
        self.result_text.see(tk.END)

    def save_to_log(self, results):
        """保存结果到日志文件（保留兼容性）"""
        try:
            log_file = os.path.join(os.path.dirname(__file__), "log.txt")

            with open(log_file, "a", encoding="utf-8") as f:
                for result in results:
                    f.write(f"时间: {result['timestamp']}\n")
                    f.write(f"密钥类型: {result['key_type']}\n")
                    f.write(f"私钥: {result['private_key']}\n")
                    f.write(f"地址类型: {result['address_type']}\n")
                    f.write(f"地址: {result['address']}\n")
                    f.write("-" * 80 + "\n")

        except Exception as e:
            messagebox.showerror("错误", f"保存日志文件失败: {str(e)}")

    def clear_results(self):
        """清空结果显示"""
        self.result_text.delete(1.0, tk.END)
        if not self.is_running:
            self.progress_var.set("")
            self.stats_var.set("就绪")

    def run(self):
        """运行GUI"""
        # 窗口关闭事件处理 - 最小化到托盘而不是退出
        def on_closing():
            if self.tray_icon:
                # 如果有托盘图标，隐藏到托盘
                self.hide_window()
            else:
                # 如果没有托盘图标，直接退出
                self.quit_application()

        self.root.protocol("WM_DELETE_WINDOW", on_closing)

        # 启动托盘图标（在后台线程中）
        if self.tray_icon:
            tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
            tray_thread.start()

        # 启动定期更新托盘提示信息
        self.update_tray_periodically()

        self.root.mainloop()

    def update_tray_periodically(self):
        """定期更新托盘提示信息"""
        self.update_tray_tooltip()
        # 每5秒更新一次
        self.root.after(5000, self.update_tray_periodically)


if __name__ == "__main__":
    app = BitcoinGeneratorGUI()
    app.run()
