#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比特币密钥和地址生成器
支持多种密钥类型和地址格式的GUI程序
增强版：支持余额查询、并发处理、循环生成
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import queue
import os
import datetime
import secrets
import hashlib
import base58
import requests
import time
import json
from typing import Tuple, Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import pystray
from PIL import Image, ImageDraw
import io
import base64


class BalanceChecker:
    """比特币地址余额查询类"""

    def __init__(self):
        # 多个API服务，提高查询成功率
        self.api_services = [
            {
                'name': 'BlockCypher',
                'url': 'https://api.blockcypher.com/v1/btc/main/addrs/{address}/balance',
                'balance_key': 'balance',
                'rate_limit': 0.2  # 每次请求间隔200ms
            },
            {
                'name': 'Blockchain.info',
                'url': 'https://blockchain.info/q/addressbalance/{address}',
                'balance_key': None,  # 直接返回余额数值
                'rate_limit': 0.3
            },
            {
                'name': 'Blockstream',
                'url': 'https://blockstream.info/api/address/{address}',
                'balance_key': 'chain_stats',
                'sub_key': 'funded_txo_sum',
                'rate_limit': 0.1
            }
        ]
        self.current_api_index = 0
        self.last_request_time = 0

    def check_balance(self, address: str, timeout: int = 10) -> Tuple[float, str]:
        """
        查询地址余额
        返回: (余额(BTC), 错误信息)
        """
        for attempt in range(len(self.api_services)):
            api = self.api_services[self.current_api_index]

            try:
                # 控制请求频率
                elapsed = time.time() - self.last_request_time
                if elapsed < api['rate_limit']:
                    time.sleep(api['rate_limit'] - elapsed)

                url = api['url'].format(address=address)
                response = requests.get(url, timeout=timeout)
                self.last_request_time = time.time()

                if response.status_code == 200:
                    balance_satoshi = self._parse_balance(response, api)
                    if balance_satoshi is not None:
                        balance_btc = balance_satoshi / 100000000  # 转换为BTC
                        return balance_btc, ""

            except Exception as e:
                error_msg = f"{api['name']} API错误: {str(e)}"

            # 切换到下一个API
            self.current_api_index = (self.current_api_index + 1) % len(self.api_services)

        return 0.0, "所有API查询失败"

    def _parse_balance(self, response: requests.Response, api: Dict[str, Any]) -> Optional[int]:
        """解析API响应中的余额"""
        try:
            if api['balance_key'] is None:
                # 直接返回数值的API
                return int(response.text.strip())
            else:
                data = response.json()
                if api['name'] == 'Blockstream' and 'sub_key' in api:
                    return data.get(api['balance_key'], {}).get(api['sub_key'], 0)
                else:
                    return data.get(api['balance_key'], 0)
        except:
            return None


class BitcoinKeyGenerator:
    """比特币密钥和地址生成器核心类"""
    
    def __init__(self):
        self.secp256k1_order = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
    
    def generate_private_key(self, key_type: str = "hex") -> str:
        """生成私钥"""
        # 生成32字节的随机数
        private_key_bytes = secrets.randbits(256).to_bytes(32, 'big')
        
        # 确保私钥在有效范围内
        private_key_int = int.from_bytes(private_key_bytes, 'big')
        if private_key_int >= self.secp256k1_order:
            private_key_int = private_key_int % self.secp256k1_order
            private_key_bytes = private_key_int.to_bytes(32, 'big')
        
        if key_type == "hex":
            return private_key_bytes.hex()
        elif key_type == "wif":
            return self._to_wif(private_key_bytes)
        elif key_type == "wif_compressed":
            return self._to_wif(private_key_bytes, compressed=True)
        else:
            return private_key_bytes.hex()
    
    def _to_wif(self, private_key_bytes: bytes, compressed: bool = False) -> str:
        """转换为WIF格式"""
        # 添加版本字节 (0x80 for mainnet)
        extended_key = b'\x80' + private_key_bytes
        
        # 如果是压缩格式，添加压缩标志
        if compressed:
            extended_key += b'\x01'
        
        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
        
        # 组合并编码
        wif_key = extended_key + checksum
        return base58.b58encode(wif_key).decode('utf-8')
    
    def generate_address(self, private_key_hex: str, address_type: str = "p2pkh") -> str:
        """根据私钥生成地址"""
        try:
            # 这里使用简化的地址生成逻辑
            # 实际应用中需要使用完整的椭圆曲线计算
            private_key_bytes = bytes.fromhex(private_key_hex)
            
            # 简化的公钥生成（实际需要椭圆曲线计算）
            public_key_hash = hashlib.sha256(private_key_bytes).digest()
            
            if address_type == "p2pkh":
                return self._generate_p2pkh_address(public_key_hash)
            elif address_type == "p2sh":
                return self._generate_p2sh_address(public_key_hash)
            elif address_type == "bech32":
                return self._generate_bech32_address(public_key_hash)
            else:
                return self._generate_p2pkh_address(public_key_hash)
        except Exception as e:
            return f"地址生成错误: {str(e)}"
    
    def _generate_p2pkh_address(self, public_key_hash: bytes) -> str:
        """生成P2PKH地址"""
        # 添加版本字节 (0x00 for mainnet P2PKH)
        versioned_hash = b'\x00' + hashlib.new('ripemd160', public_key_hash).digest()
        
        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # 组合并编码
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode('utf-8')
    
    def _generate_p2sh_address(self, public_key_hash: bytes) -> str:
        """生成P2SH地址"""
        # 添加版本字节 (0x05 for mainnet P2SH)
        versioned_hash = b'\x05' + hashlib.new('ripemd160', public_key_hash).digest()
        
        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # 组合并编码
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode('utf-8')
    
    def _generate_bech32_address(self, public_key_hash: bytes) -> str:
        """生成Bech32地址（简化版本）"""
        # 这是一个简化的bech32地址生成
        # 实际应用中需要完整的bech32编码
        hash160 = hashlib.new('ripemd160', public_key_hash).digest()
        return f"bc1q{hash160.hex()}"


class BitcoinGeneratorGUI:
    """比特币生成器GUI界面 - 增强版"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BitMiner")
        self.root.geometry("600x600")
        self.root.resizable(True, True)

        # 初始化生成器和余额查询器
        self.generator = BitcoinKeyGenerator()
        self.balance_checker = BalanceChecker()

        # 创建队列用于线程通信
        self.result_queue = queue.Queue()
        self.balance_queue = queue.Queue()

        # 控制变量
        self.is_running = False
        self.stop_event = threading.Event()
        self.executor = None

        # 统计信息
        self.stats = {
            'generated': 0,
            'checked': 0,
            'found_with_balance': 0,
            'start_time': None
        }

        # 托盘相关变量
        self.tray_icon = None
        self.is_hidden = False

        # 创建界面
        self.create_widgets()

        # 创建托盘图标
        self.create_tray_icon()

        # 启动结果处理
        self.process_results()

    def create_tray_icon(self):
        """创建系统托盘图标"""
        try:
            # 创建一个简单的比特币图标
            icon_image = self.create_bitcoin_icon()

            # 创建托盘菜单
            menu = pystray.Menu(
                pystray.MenuItem("显示程序", self.show_window),
                pystray.MenuItem("退出", self.quit_application)
            )

            # 创建托盘图标，使用default_action处理双击
            self.tray_icon = pystray.Icon(
                "BitMiner",
                icon_image,
                "BitMiner - 比特币密钥生成器",
                menu,
                default_action=self.on_tray_activate  # 双击时的默认动作
            )

        except Exception as e:
            print(f"创建托盘图标失败: {e}")
            self.tray_icon = None

    def on_tray_activate(self, icon, item):
        """处理托盘图标激活事件（双击）"""
        print("托盘图标被双击，尝试显示窗口")  # 调试信息
        # 使用线程安全的方式调用GUI方法
        self.root.after(0, self.show_window_safe)

    def show_window_safe(self):
        """线程安全的显示窗口方法"""
        try:
            if self.is_hidden:
                print("正在恢复窗口显示...")

                # 恢复窗口显示
                self.root.deiconify()

                # 确保窗口状态正常
                self.root.state('normal')

                # 确保窗口在最前面
                self.root.lift()
                self.root.attributes('-topmost', True)
                self.root.after(100, lambda: self.root.attributes('-topmost', False))

                # 获取焦点
                self.root.focus_force()

                # 更新状态
                self.is_hidden = False

                print("窗口已成功恢复显示")
            else:
                print("窗口已经显示，无需恢复")

        except Exception as e:
            print(f"恢复窗口显示时出错: {e}")

    def create_bitcoin_icon(self):
        """创建比特币图标"""
        try:
            # 创建一个32x32的图标
            size = 32
            image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(image)

            # 绘制比特币符号 ₿
            # 外圆
            draw.ellipse([2, 2, size-2, size-2], fill='#F7931A', outline='#E8831A', width=2)

            # 内部的B字符（简化版本）
            # 垂直线
            draw.rectangle([size//3, size//4, size//3+2, 3*size//4], fill='white')

            # 上半部分横线
            draw.rectangle([size//3, size//4, 2*size//3-2, size//4+2], fill='white')
            draw.rectangle([size//3, size//2-1, 2*size//3-4, size//2+1], fill='white')

            # 下半部分横线
            draw.rectangle([size//3, 3*size//4-2, 2*size//3, 3*size//4], fill='white')

            return image

        except Exception:
            # 如果创建图标失败，使用简单的彩色方块
            size = 32
            image = Image.new('RGBA', (size, size), '#F7931A')
            return image

    def show_window(self, icon=None, item=None):
        """显示主窗口（支持右键菜单调用）"""
        print("通过菜单显示窗口")
        # 使用线程安全的方法
        self.root.after(0, self.show_window_safe)

    def hide_window(self):
        """隐藏主窗口到托盘"""
        if not self.is_hidden:
            self.root.withdraw()
            self.is_hidden = True

            print("窗口已最小化到系统托盘")  # 调试信息
            print("双击托盘图标可恢复窗口显示")  # 用户提示

            # 启动托盘图标（如果还没有启动）
            if self.tray_icon and not hasattr(self.tray_icon, '_running'):
                tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
                tray_thread.start()

    def quit_application(self, icon=None, item=None):
        """退出应用程序"""
        if self.is_running:
            self.stop_generation()

        # 停止托盘图标
        if self.tray_icon:
            self.tray_icon.stop()

        # 销毁主窗口
        self.root.quit()
        self.root.destroy()

    def update_tray_tooltip(self):
        """更新托盘图标的提示信息"""
        if self.tray_icon and self.stats['start_time']:
            elapsed = time.time() - self.stats['start_time']
            hours = int(elapsed // 3600)
            minutes = int((elapsed % 3600) // 60)

            tooltip = (
                f"BitMiner - 运行中\n"
                f"运行时间: {hours:02d}:{minutes:02d}\n"
                f"已生成: {self.stats['generated']}\n"
                f"已查询: {self.stats['checked']}\n"
                f"发现有余额: {self.stats['found_with_balance']}"
            )

            try:
                self.tray_icon.title = tooltip
            except:
                pass
        elif self.tray_icon:
            self.tray_icon.title = "BitMiner - 就绪"

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(3, weight=1)

        # 第一行：密钥类型和地址类型
        ttk.Label(main_frame, text="密钥类型:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.key_type_var = tk.StringVar(value="wif")
        key_type_combo = ttk.Combobox(main_frame, textvariable=self.key_type_var,
                                     values=["wif", "hex", "wif_compressed"],
                                     state="readonly", width=15)
        key_type_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        ttk.Label(main_frame, text="地址类型:").grid(row=0, column=2, sticky=tk.W, pady=5)
        self.address_type_var = tk.StringVar(value="p2pkh")
        address_type_combo = ttk.Combobox(main_frame, textvariable=self.address_type_var,
                                         values=["p2pkh", "p2sh", "bech32"],
                                         state="readonly", width=15)
        address_type_combo.grid(row=0, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 第二行：生成参数
        ttk.Label(main_frame, text="每批生成数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.batch_size_var = tk.StringVar(value="10")
        batch_size_entry = ttk.Entry(main_frame, textvariable=self.batch_size_var, width=15)
        batch_size_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        ttk.Label(main_frame, text="查询线程数:").grid(row=1, column=2, sticky=tk.W, pady=5)
        self.thread_count_var = tk.StringVar(value="5")
        thread_count_entry = ttk.Entry(main_frame, textvariable=self.thread_count_var, width=15)
        thread_count_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 第三行：查询参数
        ttk.Label(main_frame, text="查询间隔(秒):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.query_interval_var = tk.StringVar(value="0.5")
        query_interval_entry = ttk.Entry(main_frame, textvariable=self.query_interval_var, width=15)
        query_interval_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 20))

        ttk.Label(main_frame, text="查询超时(秒):").grid(row=2, column=2, sticky=tk.W, pady=5)
        self.query_timeout_var = tk.StringVar(value="10")
        query_timeout_entry = ttk.Entry(main_frame, textvariable=self.query_timeout_var, width=15)
        query_timeout_entry.grid(row=2, column=3, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=20)

        # 开始按钮
        self.start_btn = ttk.Button(button_frame, text="开始生成和查询",
                                   command=self.start_continuous_generation)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        # 停止按钮
        self.stop_btn = ttk.Button(button_frame, text="停止",
                                  command=self.stop_generation, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 清空按钮
        clear_btn = ttk.Button(button_frame, text="清空结果", command=self.clear_results)
        clear_btn.pack(side=tk.LEFT, padx=5)

        # 统计信息框架
        stats_frame = ttk.LabelFrame(main_frame, text="运行统计", padding="10")
        stats_frame.grid(row=4, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=10)

        # 统计标签
        self.stats_var = tk.StringVar(value="就绪")
        ttk.Label(stats_frame, textvariable=self.stats_var).pack()

        # 进度信息
        self.progress_var = tk.StringVar(value="")
        ttk.Label(main_frame, textvariable=self.progress_var).grid(row=5, column=0, columnspan=4, pady=5)

        # 结果显示区域
        ttk.Label(main_frame, text="有余额的地址记录:").grid(row=6, column=0, columnspan=4, sticky=tk.W, pady=(20, 5))

        self.result_text = scrolledtext.ScrolledText(main_frame, height=15, width=120)
        self.result_text.grid(row=7, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)

        # 配置结果区域的网格权重
        main_frame.rowconfigure(7, weight=1)
    
    def start_continuous_generation(self):
        """开始连续生成和查询"""
        if self.is_running:
            return

        # 验证参数
        try:
            batch_size = int(self.batch_size_var.get())
            thread_count = int(self.thread_count_var.get())
            query_interval = float(self.query_interval_var.get())
            query_timeout = int(self.query_timeout_var.get())

            if batch_size <= 0 or batch_size > 1000:
                messagebox.showerror("错误", "每批生成数必须在1-1000之间")
                return
            if thread_count <= 0 or thread_count > 20:
                messagebox.showerror("错误", "查询线程数必须在1-20之间")
                return
            if query_interval < 0.1 or query_interval > 10:
                messagebox.showerror("错误", "查询间隔必须在0.1-10秒之间")
                return
            if query_timeout < 5 or query_timeout > 60:
                messagebox.showerror("错误", "查询超时必须在5-60秒之间")
                return

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数值")
            return

        # 初始化状态
        self.is_running = True
        self.stop_event.clear()
        self.stats['generated'] = 0
        self.stats['checked'] = 0
        self.stats['found_with_balance'] = 0
        self.stats['start_time'] = time.time()

        # 更新界面状态
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.progress_var.set("正在启动...")

        # 创建线程池
        self.executor = ThreadPoolExecutor(max_workers=thread_count)

        # 启动生成线程
        generation_thread = threading.Thread(
            target=self.continuous_generation_worker,
            args=(batch_size, query_interval, query_timeout),
            daemon=True
        )
        generation_thread.start()

    def stop_generation(self):
        """停止生成和查询"""
        if not self.is_running:
            return

        self.is_running = False
        self.stop_event.set()

        # 关闭线程池
        if self.executor:
            self.executor.shutdown(wait=False)
            self.executor = None

        # 更新界面状态
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress_var.set("已停止")

    def continuous_generation_worker(self, batch_size: int, query_interval: float, query_timeout: int):
        """连续生成工作线程"""
        try:
            while self.is_running and not self.stop_event.is_set():
                # 生成一批密钥和地址
                batch_data = self.generate_batch(batch_size)

                if not batch_data:
                    continue

                # 提交余额查询任务
                future_to_data = {}
                for data in batch_data:
                    if self.stop_event.is_set():
                        break

                    future = self.executor.submit(
                        self.check_address_balance,
                        data['address'],
                        data,
                        query_timeout
                    )
                    future_to_data[future] = data

                # 处理查询结果
                for future in as_completed(future_to_data, timeout=query_timeout + 5):
                    if self.stop_event.is_set():
                        break

                    try:
                        result = future.result()
                        if result:
                            # 发送有余额的结果
                            self.result_queue.put(('balance_found', result))
                    except Exception as e:
                        # 查询失败，记录错误但继续
                        pass

                # 等待间隔
                if not self.stop_event.wait(query_interval):
                    continue

        except Exception as e:
            self.result_queue.put(('error', f"生成过程错误: {str(e)}"))
        finally:
            self.result_queue.put(('generation_stopped', None))

    def generate_batch(self, batch_size: int) -> list:
        """生成一批密钥和地址"""
        try:
            key_type = self.key_type_var.get()
            address_type = self.address_type_var.get()

            batch_data = []

            for i in range(batch_size):
                if self.stop_event.is_set():
                    break

                # 生成私钥
                private_key = self.generator.generate_private_key(key_type)

                # 生成地址（使用hex格式的私钥）
                if key_type.startswith("wif"):
                    hex_key = self.generator.generate_private_key("hex")
                else:
                    hex_key = private_key

                address = self.generator.generate_address(hex_key, address_type)

                # 创建数据记录
                data = {
                    'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'private_key': private_key,
                    'address': address,
                    'key_type': key_type,
                    'address_type': address_type,
                    'hex_key': hex_key
                }
                batch_data.append(data)

                # 更新生成统计
                self.stats['generated'] += 1

            return batch_data

        except Exception as e:
            self.result_queue.put(('error', f"生成批次错误: {str(e)}"))
            return []

    def check_address_balance(self, address: str, data: dict, timeout: int) -> Optional[dict]:
        """检查地址余额"""
        try:
            balance, error = self.balance_checker.check_balance(address, timeout)

            # 更新查询统计
            self.stats['checked'] += 1

            # 发送进度更新
            self.result_queue.put(('progress_update', {
                'address': address,
                'balance': balance,
                'error': error
            }))

            # 只有余额大于0才返回结果
            if balance > 0:
                self.stats['found_with_balance'] += 1
                data['balance'] = balance
                data['balance_check_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                return data

            return None

        except Exception as e:
            return None


    
    def process_results(self):
        """处理生成结果（主线程）"""
        try:
            while True:
                msg_type, data = self.result_queue.get_nowait()

                if msg_type == 'progress_update':
                    self.update_progress_display(data)
                elif msg_type == 'balance_found':
                    self.display_balance_result(data)
                    self.save_balance_to_log(data)
                elif msg_type == 'generation_stopped':
                    self.progress_var.set("生成已停止")
                elif msg_type == 'error':
                    messagebox.showerror("错误", f"运行错误: {data}")
                    self.stop_generation()

        except queue.Empty:
            pass

        # 更新统计信息
        self.update_stats_display()

        # 继续检查队列
        self.root.after(100, self.process_results)

    def update_progress_display(self, data: dict):
        """更新进度显示"""
        address = data['address']
        balance = data['balance']
        error = data['error']

        if error:
            status = f"查询失败: {error}"
        else:
            status = f"余额: {balance:.8f} BTC"

        self.progress_var.set(f"正在查询 {address[:20]}... - {status}")

    def update_stats_display(self):
        """更新统计信息显示"""
        if self.stats['start_time']:
            elapsed = time.time() - self.stats['start_time']
            hours = int(elapsed // 3600)
            minutes = int((elapsed % 3600) // 60)
            seconds = int(elapsed % 60)

            generated_rate = self.stats['generated'] / elapsed if elapsed > 0 else 0
            checked_rate = self.stats['checked'] / elapsed if elapsed > 0 else 0

            stats_text = (
                f"运行时间: {hours:02d}:{minutes:02d}:{seconds:02d} | "
                f"已生成: {self.stats['generated']} ({generated_rate:.1f}/秒) | "
                f"已查询: {self.stats['checked']} ({checked_rate:.1f}/秒) | "
                f"发现有余额: {self.stats['found_with_balance']}"
            )
        else:
            stats_text = "就绪"

        self.stats_var.set(stats_text)

    def display_balance_result(self, result: dict):
        """显示有余额的结果"""
        output = f"🎉 发现有余额的地址！\n"
        output += f"生成时间: {result['timestamp']}\n"
        output += f"查询时间: {result['balance_check_time']}\n"
        output += f"余额: {result['balance']:.8f} BTC\n"
        output += f"地址: {result['address']}\n"
        output += f"地址类型: {result['address_type']}\n"
        output += f"密钥类型: {result['key_type']}\n"
        output += f"私钥: {result['private_key']}\n"
        output += "=" * 100 + "\n"

        self.result_text.insert(tk.END, output)

        # 滚动到底部
        self.result_text.see(tk.END)

        # 高亮显示
        self.result_text.tag_add("highlight", "end-9l", "end-1l")
        self.result_text.tag_config("highlight", background="yellow")

    def save_balance_to_log(self, result: dict):
        """保存有余额的结果到日志文件"""
        try:
            log_file = os.path.join(os.path.dirname(__file__), "log.txt")

            with open(log_file, "a", encoding="utf-8") as f:
                f.write(f"🎉 发现有余额的地址！\n")
                f.write(f"生成时间: {result['timestamp']}\n")
                f.write(f"查询时间: {result['balance_check_time']}\n")
                f.write(f"余额: {result['balance']:.8f} BTC\n")
                f.write(f"地址: {result['address']}\n")
                f.write(f"地址类型: {result['address_type']}\n")
                f.write(f"密钥类型: {result['key_type']}\n")
                f.write(f"私钥: {result['private_key']}\n")
                f.write("=" * 100 + "\n")

        except Exception as e:
            messagebox.showerror("错误", f"保存日志文件失败: {str(e)}")

    def display_results(self, results):
        """显示生成结果（保留兼容性）"""
        for result in results:
            output = f"时间: {result['timestamp']}\n"
            output += f"密钥类型: {result['key_type']}\n"
            output += f"私钥: {result['private_key']}\n"
            output += f"地址类型: {result['address_type']}\n"
            output += f"地址: {result['address']}\n"
            output += "-" * 80 + "\n"

            self.result_text.insert(tk.END, output)

        # 滚动到底部
        self.result_text.see(tk.END)

    def save_to_log(self, results):
        """保存结果到日志文件（保留兼容性）"""
        try:
            log_file = os.path.join(os.path.dirname(__file__), "log.txt")

            with open(log_file, "a", encoding="utf-8") as f:
                for result in results:
                    f.write(f"时间: {result['timestamp']}\n")
                    f.write(f"密钥类型: {result['key_type']}\n")
                    f.write(f"私钥: {result['private_key']}\n")
                    f.write(f"地址类型: {result['address_type']}\n")
                    f.write(f"地址: {result['address']}\n")
                    f.write("-" * 80 + "\n")

        except Exception as e:
            messagebox.showerror("错误", f"保存日志文件失败: {str(e)}")

    def clear_results(self):
        """清空结果显示"""
        self.result_text.delete(1.0, tk.END)
        if not self.is_running:
            self.progress_var.set("")
            self.stats_var.set("就绪")

    def run(self):
        """运行GUI"""
        # 窗口关闭事件处理 - 最小化到托盘而不是退出
        def on_closing():
            if self.tray_icon:
                # 如果有托盘图标，隐藏到托盘
                self.hide_window()
            else:
                # 如果没有托盘图标，直接退出
                self.quit_application()

        self.root.protocol("WM_DELETE_WINDOW", on_closing)

        # 启动托盘图标（在后台线程中）
        if self.tray_icon:
            tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
            tray_thread.start()

        # 启动定期更新托盘提示信息
        self.update_tray_periodically()

        self.root.mainloop()

    def update_tray_periodically(self):
        """定期更新托盘提示信息"""
        self.update_tray_tooltip()
        # 每5秒更新一次
        self.root.after(5000, self.update_tray_periodically)


if __name__ == "__main__":
    app = BitcoinGeneratorGUI()
    app.run()
