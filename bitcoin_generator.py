#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比特币密钥和地址生成器
支持多种密钥类型和地址格式的GUI程序
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import queue
import os
import datetime
import secrets
import hashlib
import base58
from typing import Tuple, Optional

class BitcoinKeyGenerator:
    """比特币密钥和地址生成器核心类"""
    
    def __init__(self):
        self.secp256k1_order = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
    
    def generate_private_key(self, key_type: str = "hex") -> str:
        """生成私钥"""
        # 生成32字节的随机数
        private_key_bytes = secrets.randbits(256).to_bytes(32, 'big')
        
        # 确保私钥在有效范围内
        private_key_int = int.from_bytes(private_key_bytes, 'big')
        if private_key_int >= self.secp256k1_order:
            private_key_int = private_key_int % self.secp256k1_order
            private_key_bytes = private_key_int.to_bytes(32, 'big')
        
        if key_type == "hex":
            return private_key_bytes.hex()
        elif key_type == "wif":
            return self._to_wif(private_key_bytes)
        elif key_type == "wif_compressed":
            return self._to_wif(private_key_bytes, compressed=True)
        else:
            return private_key_bytes.hex()
    
    def _to_wif(self, private_key_bytes: bytes, compressed: bool = False) -> str:
        """转换为WIF格式"""
        # 添加版本字节 (0x80 for mainnet)
        extended_key = b'\x80' + private_key_bytes
        
        # 如果是压缩格式，添加压缩标志
        if compressed:
            extended_key += b'\x01'
        
        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
        
        # 组合并编码
        wif_key = extended_key + checksum
        return base58.b58encode(wif_key).decode('utf-8')
    
    def generate_address(self, private_key_hex: str, address_type: str = "p2pkh") -> str:
        """根据私钥生成地址"""
        try:
            # 这里使用简化的地址生成逻辑
            # 实际应用中需要使用完整的椭圆曲线计算
            private_key_bytes = bytes.fromhex(private_key_hex)
            
            # 简化的公钥生成（实际需要椭圆曲线计算）
            public_key_hash = hashlib.sha256(private_key_bytes).digest()
            
            if address_type == "p2pkh":
                return self._generate_p2pkh_address(public_key_hash)
            elif address_type == "p2sh":
                return self._generate_p2sh_address(public_key_hash)
            elif address_type == "bech32":
                return self._generate_bech32_address(public_key_hash)
            else:
                return self._generate_p2pkh_address(public_key_hash)
        except Exception as e:
            return f"地址生成错误: {str(e)}"
    
    def _generate_p2pkh_address(self, public_key_hash: bytes) -> str:
        """生成P2PKH地址"""
        # 添加版本字节 (0x00 for mainnet P2PKH)
        versioned_hash = b'\x00' + hashlib.new('ripemd160', public_key_hash).digest()
        
        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # 组合并编码
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode('utf-8')
    
    def _generate_p2sh_address(self, public_key_hash: bytes) -> str:
        """生成P2SH地址"""
        # 添加版本字节 (0x05 for mainnet P2SH)
        versioned_hash = b'\x05' + hashlib.new('ripemd160', public_key_hash).digest()
        
        # 计算校验和
        checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
        
        # 组合并编码
        address_bytes = versioned_hash + checksum
        return base58.b58encode(address_bytes).decode('utf-8')
    
    def _generate_bech32_address(self, public_key_hash: bytes) -> str:
        """生成Bech32地址（简化版本）"""
        # 这是一个简化的bech32地址生成
        # 实际应用中需要完整的bech32编码
        hash160 = hashlib.new('ripemd160', public_key_hash).digest()
        return f"bc1q{hash160.hex()}"


class BitcoinGeneratorGUI:
    """比特币生成器GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("比特币密钥和地址生成器")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 初始化生成器
        self.generator = BitcoinKeyGenerator()
        
        # 创建队列用于线程通信
        self.result_queue = queue.Queue()
        
        # 创建界面
        self.create_widgets()
        
        # 启动结果处理
        self.process_results()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 密钥类型选择
        ttk.Label(main_frame, text="密钥类型:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.key_type_var = tk.StringVar(value="hex")
        key_type_combo = ttk.Combobox(main_frame, textvariable=self.key_type_var, 
                                     values=["hex", "wif", "wif_compressed"], 
                                     state="readonly", width=20)
        key_type_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 地址类型选择
        ttk.Label(main_frame, text="地址类型:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.address_type_var = tk.StringVar(value="p2pkh")
        address_type_combo = ttk.Combobox(main_frame, textvariable=self.address_type_var,
                                         values=["p2pkh", "p2sh", "bech32"],
                                         state="readonly", width=20)
        address_type_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 生成数量
        ttk.Label(main_frame, text="生成数量:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.count_var = tk.StringVar(value="1")
        count_entry = ttk.Entry(main_frame, textvariable=self.count_var, width=20)
        count_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        # 生成按钮
        self.generate_btn = ttk.Button(button_frame, text="生成密钥和地址", 
                                      command=self.start_generation)
        self.generate_btn.pack(side=tk.LEFT, padx=5)
        
        # 清空按钮
        clear_btn = ttk.Button(button_frame, text="清空结果", command=self.clear_results)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(main_frame, textvariable=self.progress_var).grid(row=4, column=0, columnspan=2, pady=5)
        
        # 结果显示区域
        ttk.Label(main_frame, text="生成结果:").grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=(20, 5))
        
        self.result_text = scrolledtext.ScrolledText(main_frame, height=20, width=80)
        self.result_text.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 配置结果区域的网格权重
        main_frame.rowconfigure(6, weight=1)
    
    def start_generation(self):
        """开始生成（在新线程中）"""
        try:
            count = int(self.count_var.get())
            if count <= 0 or count > 100000:
                messagebox.showerror("错误", "生成数量必须在1-100000之间")
                return
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数量")
            return
        
        # 禁用生成按钮
        self.generate_btn.config(state="disabled")
        self.progress_var.set("正在生成...")
        
        # 在新线程中执行生成
        thread = threading.Thread(target=self.generate_keys_and_addresses, 
                                 args=(count,), daemon=True)
        thread.start()
    
    def generate_keys_and_addresses(self, count: int):
        """生成密钥和地址（后台线程）"""
        try:
            key_type = self.key_type_var.get()
            address_type = self.address_type_var.get()
            
            results = []
            
            for i in range(count):
                # 生成私钥
                private_key = self.generator.generate_private_key(key_type)
                
                # 生成地址（使用hex格式的私钥）
                if key_type.startswith("wif"):
                    # 如果是WIF格式，需要转换回hex来生成地址
                    hex_key = self.generator.generate_private_key("hex")
                else:
                    hex_key = private_key
                
                address = self.generator.generate_address(hex_key, address_type)
                
                # 创建结果记录
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                result = {
                    'timestamp': timestamp,
                    'private_key': private_key,
                    'address': address,
                    'key_type': key_type,
                    'address_type': address_type
                }
                results.append(result)
                
                # 更新进度
                progress = f"正在生成... {i+1}/{count}"
                self.result_queue.put(('progress', progress))
            
            # 发送完成信号
            self.result_queue.put(('complete', results))
            
        except Exception as e:
            self.result_queue.put(('error', str(e)))
    
    def process_results(self):
        """处理生成结果（主线程）"""
        try:
            while True:
                msg_type, data = self.result_queue.get_nowait()
                
                if msg_type == 'progress':
                    self.progress_var.set(data)
                elif msg_type == 'complete':
                    self.display_results(data)
                    self.save_to_log(data)
                    self.progress_var.set(f"完成！生成了 {len(data)} 个密钥和地址")
                    self.generate_btn.config(state="normal")
                elif msg_type == 'error':
                    messagebox.showerror("错误", f"生成过程中出现错误: {data}")
                    self.progress_var.set("生成失败")
                    self.generate_btn.config(state="normal")
                    
        except queue.Empty:
            pass
        
        # 继续检查队列
        self.root.after(100, self.process_results)
    
    def display_results(self, results):
        """显示生成结果"""
        for result in results:
            output = f"时间: {result['timestamp']}\n"
            output += f"密钥类型: {result['key_type']}\n"
            output += f"私钥: {result['private_key']}\n"
            output += f"地址类型: {result['address_type']}\n"
            output += f"地址: {result['address']}\n"
            output += "-" * 80 + "\n"
            
            self.result_text.insert(tk.END, output)
        
        # 滚动到底部
        self.result_text.see(tk.END)
    
    def save_to_log(self, results):
        """保存结果到日志文件"""
        try:
            log_file = os.path.join(os.path.dirname(__file__), "log.txt")
            
            with open(log_file, "a", encoding="utf-8") as f:
                for result in results:
                    f.write(f"时间: {result['timestamp']}\n")
                    f.write(f"密钥类型: {result['key_type']}\n")
                    f.write(f"私钥: {result['private_key']}\n")
                    f.write(f"地址类型: {result['address_type']}\n")
                    f.write(f"地址: {result['address']}\n")
                    f.write("-" * 80 + "\n")
                    
        except Exception as e:
            messagebox.showerror("错误", f"保存日志文件失败: {str(e)}")
    
    def clear_results(self):
        """清空结果显示"""
        self.result_text.delete(1.0, tk.END)
        self.progress_var.set("就绪")
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    app = BitcoinGeneratorGUI()
    app.run()
