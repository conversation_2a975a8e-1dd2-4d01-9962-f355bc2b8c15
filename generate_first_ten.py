#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成前10个比特币地址并查询余额
详细记录到log.txt文件
"""

import sys
import os
import datetime
import time

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))

try:
    from bitcoin_generator import BitcoinKeyGenerator, BalanceChecker, ECDSA_AVAILABLE
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保bitcoin_generator.py在同一目录下")
    sys.exit(1)

def generate_and_query_first_ten():
    """生成前10个地址并查询余额"""
    print("BitMiner - 生成前10个地址并查询余额")
    print("=" * 80)
    
    if not ECDSA_AVAILABLE:
        print("❌ 错误：ecdsa库未安装")
        print("请运行: pip install ecdsa")
        return False
    
    # 初始化生成器和余额查询器
    generator = BitcoinKeyGenerator()
    balance_checker = BalanceChecker()
    
    results = []
    
    print("🚀 开始生成前10个地址并查询余额...")
    print("-" * 80)
    
    for i in range(10):
        print(f"\n📍 正在处理第 {i+1}/10 个地址...")
        
        try:
            # 生成私钥和地址
            hex_key = generator.generate_private_key("hex")
            
            # 生成WIF压缩格式私钥
            private_key_bytes = bytes.fromhex(hex_key)
            wif_compressed = generator._to_wif(private_key_bytes, compressed=True)
            
            # 生成P2PKH地址（压缩公钥）
            address = generator.generate_address(hex_key, "p2pkh", compressed=True)
            
            print(f"   🔑 私钥: {wif_compressed}")
            print(f"   🏠 地址: {address}")
            
            # 查询余额
            print(f"   🔍 正在查询余额...")
            balance, error = balance_checker.check_balance(address, timeout=20)
            
            # 记录结果
            result = {
                'index': i + 1,
                'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'private_key_hex': hex_key,
                'private_key_wif': wif_compressed,
                'address': address,
                'balance_btc': balance,
                'balance_satoshi': int(balance * 100000000) if balance > 0 else 0,
                'query_error': error,
                'query_success': error == ""
            }
            results.append(result)
            
            if result['query_success']:
                if balance > 0:
                    print(f"   🎉 余额: {balance:.8f} BTC ({result['balance_satoshi']} satoshi)")
                else:
                    print(f"   💰 余额: 0.00000000 BTC")
            else:
                print(f"   ❌ 查询失败: {error}")
            
            # 避免请求过于频繁
            if i < 9:  # 最后一个不需要等待
                print(f"   ⏳ 等待2秒避免API限制...")
                time.sleep(2)
                
        except Exception as e:
            print(f"   ❌ 处理第{i+1}个地址时出错: {e}")
            # 记录错误结果
            result = {
                'index': i + 1,
                'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'private_key_hex': 'ERROR',
                'private_key_wif': 'ERROR',
                'address': 'ERROR',
                'balance_btc': 0,
                'balance_satoshi': 0,
                'query_error': f"生成错误: {str(e)}",
                'query_success': False
            }
            results.append(result)
    
    print("\n" + "=" * 80)
    print("✅ 生成和查询完成！")
    
    # 保存详细记录到log.txt
    save_detailed_log(results)
    
    # 显示统计信息
    show_statistics(results)
    
    return True

def save_detailed_log(results):
    """保存详细记录到log.txt"""
    try:
        log_file = "log.txt"
        
        with open(log_file, "w", encoding="utf-8") as f:
            f.write("BitMiner - 比特币密钥和地址生成器\n")
            f.write("=" * 100 + "\n")
            f.write(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("前10个地址的详细记录（包含余额查询）\n")
            f.write("=" * 100 + "\n\n")
            
            # 统计信息
            successful_queries = sum(1 for r in results if r['query_success'])
            total_balance = sum(r['balance_btc'] for r in results)
            addresses_with_balance = sum(1 for r in results if r['balance_btc'] > 0)
            generation_errors = sum(1 for r in results if r['private_key_hex'] == 'ERROR')
            
            f.write("📊 统计信息:\n")
            f.write(f"总生成地址数: {len(results)}\n")
            f.write(f"生成成功数: {len(results) - generation_errors}\n")
            f.write(f"生成失败数: {generation_errors}\n")
            f.write(f"余额查询成功数: {successful_queries}\n")
            f.write(f"余额查询失败数: {len(results) - successful_queries}\n")
            f.write(f"有余额地址数: {addresses_with_balance}\n")
            f.write(f"总余额: {total_balance:.8f} BTC\n")
            if total_balance > 0:
                f.write(f"总余额(Satoshi): {int(total_balance * 100000000)}\n")
            f.write("\n" + "=" * 100 + "\n\n")
            
            # 详细记录
            for result in results:
                f.write(f"🔑 地址 #{result['index']}\n")
                f.write("-" * 80 + "\n")
                f.write(f"生成时间: {result['timestamp']}\n")
                
                if result['private_key_hex'] != 'ERROR':
                    f.write(f"私钥(HEX): {result['private_key_hex']}\n")
                    f.write(f"私钥(WIF压缩): {result['private_key_wif']}\n")
                    f.write(f"地址(P2PKH): {result['address']}\n")
                    f.write(f"地址类型: P2PKH (压缩公钥)\n")
                    
                    if result['query_success']:
                        f.write(f"✅ 余额查询: 成功\n")
                        f.write(f"余额(BTC): {result['balance_btc']:.8f}\n")
                        f.write(f"余额(Satoshi): {result['balance_satoshi']}\n")
                        
                        if result['balance_btc'] > 0:
                            f.write(f"🎉 发现有余额的地址！\n")
                            f.write(f"💎 这是一个有价值的发现！\n")
                        else:
                            f.write(f"💰 余额为零（正常情况）\n")
                    else:
                        f.write(f"❌ 余额查询: 失败\n")
                        f.write(f"错误信息: {result['query_error']}\n")
                        f.write(f"💡 建议: 可以稍后重新查询此地址\n")
                else:
                    f.write(f"❌ 地址生成失败\n")
                    f.write(f"错误信息: {result['query_error']}\n")
                
                f.write("\n" + "=" * 100 + "\n\n")
            
            # 添加验证和使用说明
            f.write("🔍 验证和使用说明:\n")
            f.write("-" * 80 + "\n")
            f.write("1. 私钥验证:\n")
            f.write("   - 可以使用任何比特币钱包导入上述私钥验证地址\n")
            f.write("   - 导入时选择'压缩公钥'格式\n")
            f.write("   - 生成的地址应该与记录中的地址完全一致\n\n")
            
            f.write("2. 推荐验证工具:\n")
            f.write("   - https://www.bitaddress.org (离线使用，最安全)\n")
            f.write("   - https://iancoleman.io/bip39/ (BIP39工具)\n")
            f.write("   - Electrum钱包 (导入私钥功能)\n")
            f.write("   - Bitcoin Core (importprivkey命令)\n\n")
            
            f.write("3. 余额查询:\n")
            f.write("   - 可以在区块链浏览器中查询地址余额\n")
            f.write("   - 推荐: blockchain.info, blockstream.info\n")
            f.write("   - 如果查询失败，可能是网络问题或API限制\n\n")
            
            f.write("4. 安全注意事项:\n")
            f.write("   ⚠️ 这些是真实的比特币私钥！\n")
            f.write("   ⚠️ 请妥善保管，不要泄露给他人\n")
            f.write("   ⚠️ 建议仅用于测试和学习目的\n")
            f.write("   ⚠️ 不建议用于存储大量资金\n\n")
            
            f.write("5. 如果发现有余额的地址:\n")
            f.write("   🎉 恭喜！这是极其罕见的情况\n")
            f.write("   📝 请立即备份私钥到安全位置\n")
            f.write("   🔒 考虑将资金转移到更安全的钱包\n")
            f.write("   📊 这种概率约为 1/2^160，极其罕见\n")
            
        print(f"✅ 详细记录已保存到 {log_file}")
        return True
        
    except Exception as e:
        print(f"❌ 保存log.txt失败: {e}")
        return False

def show_statistics(results):
    """显示统计信息"""
    print("\n📊 统计信息:")
    print("-" * 40)
    
    successful_queries = sum(1 for r in results if r['query_success'])
    total_balance = sum(r['balance_btc'] for r in results)
    addresses_with_balance = sum(1 for r in results if r['balance_btc'] > 0)
    generation_errors = sum(1 for r in results if r['private_key_hex'] == 'ERROR')
    
    print(f"总生成地址数: {len(results)}")
    print(f"生成成功数: {len(results) - generation_errors}")
    print(f"余额查询成功数: {successful_queries}")
    print(f"有余额地址数: {addresses_with_balance}")
    print(f"总余额: {total_balance:.8f} BTC")
    
    if addresses_with_balance > 0:
        print(f"🎉 发现 {addresses_with_balance} 个有余额的地址！")
    else:
        print("💰 所有地址余额为零（这是正常情况）")

def main():
    """主函数"""
    try:
        success = generate_and_query_first_ten()
        
        if success:
            print("\n🎯 任务完成！")
            print("📄 详细记录已保存到 log.txt 文件")
            print("🔍 您可以使用任何比特币钱包验证这些私钥和地址")
        else:
            print("\n❌ 任务失败")
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 任务被用户中断")
    except Exception as e:
        print(f"\n\n❌ 执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
