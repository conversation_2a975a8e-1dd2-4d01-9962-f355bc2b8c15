# BitMiner - 比特币密钥和地址生成器

这是一个使用Python和tkinter开发的比特币密钥和地址生成器GUI程序，具有余额查询和系统托盘功能。

## 功能特性

### 核心功能
- **随机生成比特币私钥**：支持多种格式（HEX、WIF、WIF压缩格式）
- **生成对应地址**：支持P2PKH、P2SH、Bech32地址格式
- **余额查询功能**：自动查询生成地址的余额
- **并发处理**：支持多线程并发生成和查询
- **智能记录**：只记录有余额的地址，节省存储空间

### 界面功能
- **用户友好界面**：使用tkinter创建的直观GUI
- **异步处理**：生成和查询过程在后台线程执行，界面不会卡死
- **实时统计**：显示运行时间、生成数量、查询数量等统计信息
- **系统托盘**：支持最小化到系统托盘，后台运行

### 高级功能
- **连续运行**：支持循环不断生成、查询、记录
- **用户自定义参数**：可配置生成数量、查询线程数、查询间隔等
- **多API支持**：使用多个区块链API服务，提高查询成功率
- **自动日志记录**：有余额的地址自动保存到log.txt文件

## 安装要求

- Python 3.6+
- tkinter（通常随Python一起安装）
- 必需的Python库（见requirements.txt）

## 安装步骤

1. 确保已安装Python 3.6或更高版本
2. 安装依赖项：
   ```bash
   pip install -r requirements.txt
   ```

## 使用方法

### 基本使用

1. 运行程序：
   ```bash
   python bitcoin_generator.py
   ```

2. 在GUI界面中配置参数：
   - **密钥类型**：选择wif、hex或wif_compressed格式
   - **地址类型**：选择p2pkh、p2sh或bech32格式
   - **每批生成数**：每次生成的密钥数量（建议10-100）
   - **查询线程数**：并发查询的线程数（建议5-10）
   - **查询间隔**：每批查询之间的间隔时间（秒）
   - **查询超时**：单次查询的超时时间（秒）

3. 点击"开始生成和查询"按钮开始运行

4. 程序将：
   - 连续生成比特币密钥和地址
   - 自动查询每个地址的余额
   - 只记录有余额的地址到log.txt文件
   - 在界面显示实时统计信息

### 系统托盘功能

- **最小化到托盘**：点击窗口关闭按钮，程序将最小化到系统托盘
- **查看状态**：鼠标悬停在托盘图标上可查看运行状态
- **右键菜单**：右键点击托盘图标可选择"显示程序"或"退出"
- **后台运行**：程序可在托盘中后台运行，不影响其他操作

### 测试托盘功能

如果需要单独测试托盘功能，可以运行：
```bash
python test_tray.py
```

## 密钥类型说明

- **hex**：64位十六进制格式的私钥
- **wif**：钱包导入格式（Wallet Import Format）
- **wif_compressed**：压缩格式的WIF私钥

## 地址类型说明

- **p2pkh**：Pay-to-Public-Key-Hash，传统的比特币地址（以1开头）
- **p2sh**：Pay-to-Script-Hash，脚本哈希地址（以3开头）
- **bech32**：新的地址格式（以bc1开头）

## 安全提示

⚠️ **重要安全提示**：
- 这个程序仅用于学习和测试目的
- 生成的私钥会保存在log.txt文件中，请妥善保管
- 在生产环境中使用时，请确保私钥的安全性
- 不要在不安全的环境中运行此程序

## 文件说明

- `bitcoin_generator.py`：主程序文件
- `requirements.txt`：Python依赖项列表
- `log.txt`：自动生成的日志文件（包含所有生成的密钥和地址）

## 注意事项

1. 程序使用Python的`secrets`模块生成加密安全的随机数
2. 地址生成使用了简化的算法，实际应用中可能需要更完整的椭圆曲线计算
3. 生成过程在后台线程中执行，确保界面响应性
4. 支持批量生成，但建议单次生成数量不超过1000个以保证性能

## 许可证

此项目仅供学习和研究使用。
