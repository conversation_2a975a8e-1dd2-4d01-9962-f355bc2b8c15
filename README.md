# 比特币密钥和地址生成器

这是一个使用Python和tkinter开发的比特币密钥和地址生成器GUI程序。

## 功能特性

- **随机生成比特币私钥**：支持多种格式（HEX、WIF、WIF压缩格式）
- **生成对应地址**：支持P2PKH、P2SH、Bech32地址格式
- **用户友好界面**：使用tkinter创建的直观GUI
- **异步处理**：生成过程在后台线程执行，界面不会卡死
- **自动日志记录**：所有生成的密钥和地址自动保存到log.txt文件
- **批量生成**：支持一次生成多个密钥和地址对

## 安装要求

- Python 3.6+
- tkinter（通常随Python一起安装）
- base58库

## 安装步骤

1. 确保已安装Python 3.6或更高版本
2. 安装依赖项：
   ```bash
   pip install -r requirements.txt
   ```

## 使用方法

1. 运行程序：
   ```bash
   python bitcoin_generator.py
   ```

2. 在GUI界面中：
   - 选择密钥类型（hex、wif、wif_compressed）
   - 选择地址类型（p2pkh、p2sh、bech32）
   - 输入要生成的数量（1-1000）
   - 点击"生成密钥和地址"按钮

3. 生成的结果将显示在界面上，并自动保存到当前目录的`log.txt`文件中

## 密钥类型说明

- **hex**：64位十六进制格式的私钥
- **wif**：钱包导入格式（Wallet Import Format）
- **wif_compressed**：压缩格式的WIF私钥

## 地址类型说明

- **p2pkh**：Pay-to-Public-Key-Hash，传统的比特币地址（以1开头）
- **p2sh**：Pay-to-Script-Hash，脚本哈希地址（以3开头）
- **bech32**：新的地址格式（以bc1开头）

## 安全提示

⚠️ **重要安全提示**：
- 这个程序仅用于学习和测试目的
- 生成的私钥会保存在log.txt文件中，请妥善保管
- 在生产环境中使用时，请确保私钥的安全性
- 不要在不安全的环境中运行此程序

## 文件说明

- `bitcoin_generator.py`：主程序文件
- `requirements.txt`：Python依赖项列表
- `log.txt`：自动生成的日志文件（包含所有生成的密钥和地址）

## 注意事项

1. 程序使用Python的`secrets`模块生成加密安全的随机数
2. 地址生成使用了简化的算法，实际应用中可能需要更完整的椭圆曲线计算
3. 生成过程在后台线程中执行，确保界面响应性
4. 支持批量生成，但建议单次生成数量不超过1000个以保证性能

## 许可证

此项目仅供学习和研究使用。
