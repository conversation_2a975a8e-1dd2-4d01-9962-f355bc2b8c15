# API查询错误修复说明

## 问题描述

之前的版本存在一个严重问题：当API返回错误信息（如"Rate limited"）时，程序错误地将其当作余额为零处理。这导致：

1. **误判余额**：将API错误当作零余额
2. **记录错误数据**：可能记录实际有余额但API查询失败的地址
3. **统计不准确**：查询失败被计入成功统计

## 修复内容

### 1. 改进错误检测

**新增错误响应检测函数：**
```python
def _is_error_response(self, response_text: str) -> bool:
    """检查响应文本是否是错误信息"""
    error_indicators = [
        'rate limited', 'rate limit', 'too many requests',
        'error', 'invalid', 'not found', 'service unavailable',
        'timeout', 'forbidden', 'unauthorized'
    ]
    response_lower = response_text.lower()
    return any(indicator in response_lower for indicator in error_indicators)
```

### 2. 重构余额解析

**修改前：**
- 直接尝试将响应转换为数字
- 失败时返回 `None`，被当作零余额处理

**修改后：**
- 先检查是否是错误响应
- 返回详细的结果字典：`{'success': bool, 'balance': int, 'error': str}`
- 明确区分查询失败和余额为零

### 3. 改进API管理

**新增功能：**
- 为每个API单独记录请求时间
- 添加User-Agent头部
- 调整API优先级（Blockstream优先，blockchain.info最后）
- 增加详细的调试日志

### 4. 增强错误处理

**改进内容：**
- 区分不同类型的HTTP错误
- 详细记录失败原因
- 避免将API错误当作成功查询

## 测试验证

### 运行API测试
```bash
python test_balance_api.py
```

### 测试场景
1. **正常地址查询**：验证有余额地址的正确查询
2. **零余额地址**：确认零余额和查询失败的区别
3. **错误响应处理**：测试"Rate limited"等错误的正确处理
4. **API切换**：验证多API服务的故障转移

## 主要改进点

### 1. 精确的错误识别
- ✅ 正确识别"Rate limited"为查询失败
- ✅ 区分真正的零余额和API错误
- ✅ 支持多种错误响应格式

### 2. 可靠的API轮换
- ✅ 智能API选择和切换
- ✅ 独立的请求频率控制
- ✅ 详细的失败原因记录

### 3. 准确的统计信息
- ✅ 只有成功查询才计入统计
- ✅ 失败查询不影响余额统计
- ✅ 详细的错误日志记录

## 使用建议

### 1. 监控日志输出
程序现在会输出详细的API查询日志：
```
正在使用 Blockstream API查询地址: 13AM4VW2dhxYgXeQepo...
✅ Blockstream 查询成功: 0.00000000 BTC
```

### 2. 理解错误信息
- `❌ API解析失败`: API返回了错误响应
- `⚠️ 请求频率限制`: 需要等待更长时间
- `✅ 查询成功`: 成功获取余额（可能为零）

### 3. 调整查询参数
如果经常遇到频率限制：
- 增加"查询间隔"时间
- 减少"查询线程数"
- 增加"查询超时"时间

## 注意事项

1. **网络环境**：确保网络连接稳定
2. **API限制**：不同API有不同的频率限制
3. **地址格式**：确保使用有效的比特币地址格式
4. **耐心等待**：API查询需要时间，特别是在网络较慢时

## 故障排除

### 问题：所有API都返回错误
**可能原因：**
- 网络连接问题
- 防火墙阻止
- API服务暂时不可用

**解决方案：**
- 检查网络连接
- 尝试使用VPN
- 等待一段时间后重试

### 问题：查询速度很慢
**可能原因：**
- 网络延迟高
- API响应慢
- 频率限制过于保守

**解决方案：**
- 适当减少查询间隔
- 增加查询超时时间
- 检查网络质量
