#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试BIP39助记词功能
验证助记词生成、验证和地址派生是否正确
"""

import sys
import os
import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))

try:
    from bitcoin_generator import MnemonicGenerator, BIP39_AVAILABLE
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保bitcoin_generator.py在同一目录下")
    sys.exit(1)

def test_mnemonic_generation():
    """测试助记词生成功能"""
    print("BIP39助记词功能测试")
    print("=" * 60)
    
    if not BIP39_AVAILABLE:
        print("❌ 错误：mnemonic库未安装")
        print("请运行: pip install mnemonic")
        return False
    
    try:
        generator = MnemonicGenerator()
        
        # 测试不同长度的助记词
        test_cases = [
            {"length": 12, "strength": 128},
            {"length": 15, "strength": 160},
            {"length": 18, "strength": 192},
            {"length": 21, "strength": 224},
            {"length": 24, "strength": 256}
        ]
        
        results = []
        
        print("生成不同长度的助记词...")
        print("-" * 60)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {test_case['length']} 词助记词")
            
            try:
                # 生成助记词
                mnemonic = generator.generate_mnemonic(test_case['strength'])
                words = mnemonic.split()
                
                print(f"✅ 生成成功，词数: {len(words)}")
                print(f"助记词: {mnemonic}")
                
                # 验证助记词
                is_valid = generator.validate_mnemonic(mnemonic)
                print(f"✅ 验证结果: {'有效' if is_valid else '无效'}")
                
                if is_valid:
                    # 生成钱包信息
                    wallet_info = generator.generate_wallet_from_mnemonic(mnemonic, "p2pkh")
                    
                    print(f"✅ 地址: {wallet_info['address']}")
                    print(f"✅ 私钥(WIF): {wallet_info['private_key_wif']}")
                    
                    # 记录结果
                    result = {
                        'index': i,
                        'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        'length': test_case['length'],
                        'mnemonic': mnemonic,
                        'wallet_info': wallet_info,
                        'valid': is_valid
                    }
                    results.append(result)
                
            except Exception as e:
                print(f"❌ 生成失败: {e}")
                import traceback
                traceback.print_exc()
        
        return results
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def test_known_mnemonic():
    """测试已知助记词的地址生成"""
    print(f"\n{'='*60}")
    print("测试已知助记词的地址生成")
    print("-" * 60)
    
    if not BIP39_AVAILABLE:
        return False
    
    try:
        generator = MnemonicGenerator()
        
        # 使用一个已知的测试助记词
        test_mnemonic = "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about"
        
        print(f"测试助记词: {test_mnemonic}")
        
        # 验证助记词
        is_valid = generator.validate_mnemonic(test_mnemonic)
        print(f"验证结果: {'有效' if is_valid else '无效'}")
        
        if is_valid:
            # 生成不同类型的地址
            address_types = ["p2pkh", "p2sh", "bech32"]
            
            for addr_type in address_types:
                try:
                    wallet_info = generator.generate_wallet_from_mnemonic(test_mnemonic, addr_type)
                    print(f"\n{addr_type.upper()} 地址:")
                    print(f"  地址: {wallet_info['address']}")
                    print(f"  私钥(HEX): {wallet_info['private_key_hex']}")
                    print(f"  私钥(WIF): {wallet_info['private_key_wif']}")
                    
                except Exception as e:
                    print(f"❌ 生成 {addr_type} 地址失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试已知助记词失败: {e}")
        return False

def save_test_results(results):
    """保存测试结果到文件"""
    if not results:
        return False
    
    try:
        log_file = "mnemonic_test_log.txt"
        
        with open(log_file, "w", encoding="utf-8") as f:
            f.write("BIP39助记词功能测试结果\n")
            f.write("=" * 80 + "\n")
            f.write(f"测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总计测试: {len(results)} 个助记词\n")
            f.write("=" * 80 + "\n\n")
            
            for result in results:
                f.write(f"测试 #{result['index']}\n")
                f.write("-" * 40 + "\n")
                f.write(f"时间: {result['timestamp']}\n")
                f.write(f"长度: {result['length']} 词\n")
                f.write(f"助记词: {result['mnemonic']}\n")
                f.write(f"验证: {'通过' if result['valid'] else '失败'}\n")
                
                if result['valid'] and 'wallet_info' in result:
                    wallet = result['wallet_info']
                    f.write(f"地址: {wallet['address']}\n")
                    f.write(f"地址类型: {wallet['address_type']}\n")
                    f.write(f"私钥(HEX): {wallet['private_key_hex']}\n")
                    f.write(f"私钥(WIF): {wallet['private_key_wif']}\n")
                    f.write(f"种子: {wallet['seed']}\n")
                
                f.write("\n" + "=" * 80 + "\n\n")
            
            f.write("验证说明:\n")
            f.write("-" * 40 + "\n")
            f.write("1. 可以使用支持BIP39的钱包导入助记词验证\n")
            f.write("2. 推荐验证工具:\n")
            f.write("   - https://iancoleman.io/bip39/\n")
            f.write("   - Electrum钱包\n")
            f.write("   - MetaMask等支持BIP39的钱包\n")
            f.write("3. 注意: 这些是真实的助记词，请妥善保管！\n")
        
        print(f"✅ 测试结果已保存到 {log_file}")
        return True
        
    except Exception as e:
        print(f"❌ 保存测试结果失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 测试助记词生成
        results = test_mnemonic_generation()
        
        if results:
            # 测试已知助记词
            test_known_mnemonic()
            
            # 保存测试结果
            save_test_results(results)
            
            print(f"\n✅ 测试完成，共生成 {len(results)} 个助记词")
            print("📄 详细结果已保存到 mnemonic_test_log.txt 文件")
            print("🔍 您可以使用BIP39兼容的钱包验证这些助记词")
        else:
            print("❌ 测试失败")
            
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
