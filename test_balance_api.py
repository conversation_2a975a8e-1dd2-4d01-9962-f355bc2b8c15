#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试余额查询API的脚本
用于验证API错误处理是否正确
"""

import sys
import os
import time

# 添加当前目录到路径，以便导入主程序模块
sys.path.insert(0, os.path.dirname(__file__))

try:
    from bitcoin_generator import BalanceChecker
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保bitcoin_generator.py在同一目录下")
    sys.exit(1)

def test_balance_checker():
    """测试余额查询功能"""
    print("余额查询API测试")
    print("=" * 50)
    
    checker = BalanceChecker()
    
    # 测试地址列表
    test_addresses = [
        {
            'address': '**********************************',
            'description': '已知有余额的地址'
        },
        {
            'address': '**********************************',
            'description': '创世区块地址（有余额）'
        },
        {
            'address': '***************************',
            'description': '无效地址（测试错误处理）'
        },
        {
            'address': '**********************************',
            'description': '随机地址（可能无余额）'
        }
    ]
    
    for i, test_case in enumerate(test_addresses, 1):
        address = test_case['address']
        description = test_case['description']
        
        print(f"\n测试 {i}: {description}")
        print(f"地址: {address}")
        print("-" * 40)
        
        start_time = time.time()
        balance, error = checker.check_balance(address, timeout=15)
        end_time = time.time()
        
        print(f"查询时间: {end_time - start_time:.2f} 秒")
        
        if error:
            print(f"❌ 查询失败: {error}")
        else:
            print(f"✅ 查询成功")
            print(f"余额: {balance:.8f} BTC")
            if balance > 0:
                print(f"余额: {balance * *********:.0f} satoshi")
        
        # 避免请求过于频繁
        if i < len(test_addresses):
            print("等待1秒...")
            time.sleep(1)
    
    print("\n" + "=" * 50)
    print("测试完成")

def test_error_detection():
    """测试错误检测功能"""
    print("\n错误检测测试")
    print("=" * 50)
    
    checker = BalanceChecker()
    
    # 测试错误响应检测
    error_responses = [
        "Rate limited",
        "Too many requests",
        "Error: Invalid address",
        "Service unavailable",
        "Not found",
        "12345",  # 正常数字
        "0",      # 零余额
        "invalid response"
    ]
    
    for response in error_responses:
        is_error = checker._is_error_response(response)
        status = "❌ 错误" if is_error else "✅ 正常"
        print(f"{status}: '{response}'")

def main():
    """主函数"""
    print("比特币余额查询API测试工具")
    print("此工具用于测试API错误处理是否正确")
    print()
    
    try:
        # 测试错误检测
        test_error_detection()
        
        # 测试余额查询
        test_balance_checker()
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
