#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试私钥和地址生成的正确性
生成前10个私钥和对应地址，记录到log.txt文件
"""

import sys
import os
import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))

try:
    from bitcoin_generator import BitcoinKeyGenerator, ECDSA_AVAILABLE
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保bitcoin_generator.py在同一目录下")
    sys.exit(1)

def test_key_generation():
    """测试密钥和地址生成"""
    print("比特币密钥和地址生成测试")
    print("=" * 60)
    
    if not ECDSA_AVAILABLE:
        print("❌ 错误：ecdsa库未安装")
        print("请运行: pip install ecdsa")
        return False
    
    generator = BitcoinKeyGenerator()
    
    # 测试不同的密钥和地址类型
    test_cases = [
        {"key_type": "hex", "address_type": "p2pkh", "compressed": True},
        {"key_type": "wif", "address_type": "p2pkh", "compressed": False},
        {"key_type": "wif_compressed", "address_type": "p2pkh", "compressed": True},
        {"key_type": "hex", "address_type": "p2sh", "compressed": True},
        {"key_type": "hex", "address_type": "bech32", "compressed": True},
    ]
    
    results = []
    
    print("生成测试密钥和地址...")
    print("-" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case}")
        
        try:
            # 生成私钥
            if test_case["key_type"] == "hex":
                hex_key = generator.generate_private_key("hex")
                private_key = hex_key
            elif test_case["key_type"] == "wif":
                hex_key = generator.generate_private_key("hex")
                private_key_bytes = bytes.fromhex(hex_key)
                private_key = generator._to_wif(private_key_bytes, compressed=False)
            elif test_case["key_type"] == "wif_compressed":
                hex_key = generator.generate_private_key("hex")
                private_key_bytes = bytes.fromhex(hex_key)
                private_key = generator._to_wif(private_key_bytes, compressed=True)
            
            # 生成地址
            address = generator.generate_address(
                hex_key, 
                test_case["address_type"], 
                test_case["compressed"]
            )
            
            # 记录结果
            result = {
                'index': i,
                'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'key_type': test_case["key_type"],
                'address_type': test_case["address_type"],
                'compressed': test_case["compressed"],
                'private_key': private_key,
                'hex_key': hex_key,
                'address': address
            }
            results.append(result)
            
            print(f"✅ 私钥: {private_key}")
            print(f"✅ 地址: {address}")
            
        except Exception as e:
            print(f"❌ 生成失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 额外生成5个标准的压缩WIF私钥和P2PKH地址
    print(f"\n生成额外的5个标准密钥对...")
    print("-" * 60)
    
    for i in range(5):
        try:
            hex_key = generator.generate_private_key("hex")
            private_key_bytes = bytes.fromhex(hex_key)
            wif_compressed = generator._to_wif(private_key_bytes, compressed=True)
            address = generator.generate_address(hex_key, "p2pkh", compressed=True)
            
            result = {
                'index': len(results) + 1,
                'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'key_type': "wif_compressed",
                'address_type': "p2pkh",
                'compressed': True,
                'private_key': wif_compressed,
                'hex_key': hex_key,
                'address': address
            }
            results.append(result)
            
            print(f"密钥对 {i+1}:")
            print(f"  私钥(WIF): {wif_compressed}")
            print(f"  地址(P2PKH): {address}")
            
        except Exception as e:
            print(f"❌ 生成密钥对 {i+1} 失败: {e}")
    
    return results

def save_to_log(results):
    """保存结果到log.txt文件"""
    try:
        log_file = "log.txt"
        
        with open(log_file, "w", encoding="utf-8") as f:
            f.write("比特币密钥和地址生成测试结果\n")
            f.write("=" * 80 + "\n")
            f.write(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总计生成: {len(results)} 个密钥对\n")
            f.write("=" * 80 + "\n\n")
            
            for result in results:
                f.write(f"序号: {result['index']}\n")
                f.write(f"时间: {result['timestamp']}\n")
                f.write(f"密钥类型: {result['key_type']}\n")
                f.write(f"地址类型: {result['address_type']}\n")
                f.write(f"压缩格式: {result['compressed']}\n")
                f.write(f"私钥: {result['private_key']}\n")
                f.write(f"私钥(HEX): {result['hex_key']}\n")
                f.write(f"地址: {result['address']}\n")
                f.write("-" * 80 + "\n")
        
        print(f"\n✅ 结果已保存到 {log_file}")
        return True
        
    except Exception as e:
        print(f"❌ 保存日志文件失败: {e}")
        return False

def verify_with_external_tools():
    """提供外部验证建议"""
    print("\n" + "=" * 60)
    print("外部验证建议:")
    print("=" * 60)
    print("1. 使用在线工具验证:")
    print("   - https://www.bitaddress.org (离线使用)")
    print("   - https://iancoleman.io/bip39/ (BIP39工具)")
    print()
    print("2. 使用比特币钱包导入私钥验证地址")
    print("3. 使用命令行工具验证:")
    print("   - Bitcoin Core的bitcoin-cli")
    print("   - 其他比特币库")
    print()
    print("⚠️ 注意：这些是测试密钥，不要用于实际资金！")

def main():
    """主函数"""
    try:
        # 测试密钥生成
        results = test_key_generation()
        
        if results:
            # 保存到日志文件
            save_to_log(results)
            
            # 显示验证建议
            verify_with_external_tools()
            
            print(f"\n✅ 测试完成，共生成 {len(results)} 个密钥对")
        else:
            print("❌ 测试失败")
            
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
